/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/login/index" |
       "/pages/meet/apply" |
       "/pages/meet/detail" |
       "/pages/meet/info" |
       "/pages/news/detail" |
       "/pages/news/index" |
       "/pages/notice/detail" |
       "/pages/notice/index" |
       "/pages/opinion/collect" |
       "/pages/person/index" |
       "/pages/problem/submit" |
       "/pages/work/index" |
       "/pages/news/_comp/detailHeader" |
       "/pages/notice/_comp/detailHeader" |
       "/pages/opinion/summary/detail" |
       "/pages/opinion/summary/index" |
       "/pages/person/password/index" |
       "/pages/problem/list/detail" |
       "/pages/problem/list/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/work/index" | "/pages/person/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
