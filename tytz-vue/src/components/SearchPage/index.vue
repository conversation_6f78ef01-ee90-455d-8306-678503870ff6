<script setup lang="ts" generic="R extends object, T extends R">
import { reactive, toRaw, useTemplateRef, watch } from "vue";
import { useRequest } from "vue-hooks-plus";

import CustomComponent from "@/components/CustomComponent/index.vue";
import Form from "@/components/Form/index.vue";
import Pagination from "@/components/Pagination/index.vue";
import Table from "@/components/Table/index.vue";
import { PAGING } from "@/const/paging";
import { RESULT_LIST } from "@/const/result";
import useDefaultPaging from "@/hooks/useDefaultPaging";
import useDefaultResultList from "@/hooks/useDefaultResultList";

import { useChildrenAttrs, useChildrenSlots } from "./hook";
import type { SearchPageProps } from "./type";

const props = defineProps<SearchPageProps<R, T>>();

const childrenAttrs = useChildrenAttrs();
const childrenSlots = useChildrenSlots();

const tableRef = useTemplateRef("tableRef");

const formData = reactive({});

const page = useDefaultPaging();
const { data, refresh, loading } = useRequest(
  () => {
    return props.service({
      ...page,
      ...formData,
    });
  },
  {
    initialData: toRaw(useDefaultResultList()),
    refreshDeps: true,
  }
);

// 每次刷新数据时 表格回顶
watch(data, () => {
  tableRef.value?.scrollTo({
    top: 0,
    left: 0,
    behavior: "smooth",
  });
});

function handleFormSubmit(formatData: any) {
  page[PAGING.PAGE_NUM] = 1;
  Object.assign(formData, formatData);
}
function handleFormRefresh() {
  refresh();
}

defineExpose({
  refresh: handleFormRefresh,
  getFormData: () => toRaw(formData),
});
</script>

<template>
  <div class="search-page">
    <slot name="form" :submit="handleFormSubmit">
      <div v-if="props.formItems && props.formItems.length > 0" class="form">
        <Form
          v-bind="childrenAttrs.form"
          :items="props.formItems"
          class="form-container"
          @submit="handleFormSubmit"
          @refresh="handleFormRefresh"
        >
          <template v-for="(name, key) in childrenSlots.form" :key="key" #[key]="scoped">
            <slot :name="name" v-bind="scoped" />
          </template>
        </Form>
      </div>
    </slot>

    <slot name="operations">
      <div v-if="props.operations && props.operations.length > 0" class="operations">
        <CustomComponent v-for="(item, index) in props.operations" :key="index" v-bind="item" />
      </div>
    </slot>

    <slot name="table" :loading="loading" :submit="handleFormSubmit">
      <div v-loading="loading" class="content">
        <Table
          ref="tableRef"
          height="100%"
          v-bind="childrenAttrs.table"
          :data="data?.[RESULT_LIST.LIST] || []"
          :columns="props.tableColumns || {}"
        >
          <template v-for="(name, key) in childrenSlots.table" :key="key" #[key]="scoped">
            <slot :name="name" v-bind="scoped" />
          </template>
        </Table>
      </div>
    </slot>

    <slot name="pagination">
      <Pagination
        v-model:limit="page[PAGING.PAGE_SIZE]"
        v-model:page="page[PAGING.PAGE_NUM]"
        :total="Number(data?.[RESULT_LIST.TOTAL] || 0)"
        class="pagination-container"
      />
    </slot>
  </div>
</template>

<style scoped>
.search-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.form-container {
  width: 100%;
  margin-bottom: 20px;
}

.content {
  flex: 1;
  overflow-y: auto;
}
.form {
  padding: 10px 0;
}
.operations {
  padding: 0px 0;
  padding-bottom: 10px;
}
</style>
