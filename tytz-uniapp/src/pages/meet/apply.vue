<route lang="json5">
{
  style: {
    navigationBarTitleText: '约见申请',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { useNewUserStoreHook } from '@/store/modules/user'
import { createAppointment } from '@/api/meet/index'
import dayjs from 'dayjs'
import { getUserListByDeptId } from '@/api/user'
defineOptions({
  name: 'MeetApply',
})

interface memberDeptOption {
  value: string
  label: string
}

const appointmentDepartmentOptions = ref<memberDeptOption[]>([])
const userStore = useNewUserStoreHook()
const formDataList = [
  {
    title: '约见人名称',
    prop: 'appointmentName',
    type: 'input',
    value: userStore.userInfo.nickname,
  },
  {
    title: '所属单位',
    prop: 'appointmentUnit',
    type: 'input',
    value: userStore.userInfo.company,
  },
  {
    title: '联系方式',
    prop: 'appointmentContact',
    type: 'input',
    value: userStore.userInfo.mobile,
    rule: {
      title: '请输入正确的手机号码',
      visible(value: string) {
        // 检验手机格式
        return /^1[3-9]\d{9}$/.test(value)
      },
    },
  },
  {
    title: '约见部委',
    prop: 'appointmentDepartment',
    type: 'option',
    value: '',
  },
  {
    title: '约见时间',
    prop: 'appointmentTime',
    type: 'date',
    value: '2024-01-01',
  },
  {
    title: '约见原因',
    prop: 'appointmentReason',
    type: 'textarea',
  },
]
const formData = reactive(
  Object.fromEntries(formDataList.map((item) => [item.prop, item.value || ''])),
)

const submit = async () => {
  for (const key of formDataList) {
    if (!formData[key.prop]) {
      uni.showToast({
        title: '请输入' + key.title,
        icon: 'none',
      })
      return
    }
  }
  if (!appointmentStartTime.value || !appointmentEndTime.value) {
    uni.showToast({
      title: '请选择约见时间',
      icon: 'none',
    })
    return
  }
  for (const key of formDataList) {
    if (key.rule) {
      if (!key.rule.visible(formData[key.prop])) {
        uni.showToast({
          title: key.rule.title,
          icon: 'none',
          duration: 2000,
        })
        return
      }
    }
  }

  const parmas = {
    ...formData,
    memberRole: userStore.role.id,
    appointmentTime: '2025-01-01',
    appointmentStartTime: dayjs(appointmentStartTime.value).format('YYYY-MM-DDTHH:mm:ss'),
    appointmentEndTime: dayjs(appointmentEndTime.value).format('YYYY-MM-DDTHH:mm:ss'),
  }
  delete parmas.appointmentTime
  await createAppointment(parmas)
  uni.navigateBack().then(() => {
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })
  })
}
const appointmentStartTime = ref<number>()

const appointmentEndTime = ref<number>()
const deptNameText = ref('')
const getAppointmentDepartment = async () => {
  const result = await getUserListByDeptId('4')
  appointmentDepartmentOptions.value =
    result.map((item) => ({
      value: item.username,
      label: item.nickname,
    })) || []
}

const handleChange = (e: any) => {
  const index = e.detail.value
  if (index >= 0 && appointmentDepartmentOptions.value.length > 0) {
    const option = appointmentDepartmentOptions.value[index]
    formData.appointmentDepartment = String(option.value)
    deptNameText.value = option.label
  }
}

getAppointmentDepartment()
</script>

<template>
  <PageContainer theme="page">
    <view class="px-4 py-3">
      <view class="mb-4" v-for="item in formDataList" :key="item.prop">
        <view class="text-[14px] text-[#333] mb-2">
          {{ item.title }}
          <text class="text-#F51414">*</text>
        </view>
        <input
          v-if="item.type === 'input'"
          v-model="formData[item.prop]"
          class="w-full h-[44px] rounded border border-solid border-#A6A6A6 box-border px-20rpx"
          :placeholder="'请输入' + item.title"
          placeholder-class="text-gray-400 "
        />
        <view v-else-if="item.type === 'date'" class="w-full px-20rpx date-picker flex">
          <wd-datetime-picker
            v-model="appointmentStartTime"
            label=""
            placeholder="开始日期"
            custom-cell-class="cell"
            :min-date="Date.now()"
          />
          <view class="mt-20rpx">至</view>
          <wd-datetime-picker
            v-model="appointmentEndTime"
            label=""
            placeholder="结束日期"
            custom-cell-class="cell"
            :min-date="appointmentStartTime"
          />
        </view>

        <picker
          v-else-if="item.type === 'option'"
          @change="handleChange"
          :range="appointmentDepartmentOptions"
          range-key="label"
          class="w-full"
        >
          <view
            class="w-full min-h-[80rpx] rounded border border-solid border-#A6A6A6 box-border px-20rpx flex items-center break-all"
          >
            <text v-if="deptNameText" class="text-#333">{{ deptNameText }}</text>
            <text v-else class="text-gray-400">请选择营约见部委</text>
          </view>
        </picker>
        <textarea
          v-else-if="item.type === 'textarea'"
          v-model="formData[item.prop]"
          class="w-full min-h-[200px] rounded border border-solid border-#A6A6A6 box-border p-20rpx"
          :placeholder="'请输入' + item.title"
          placeholder-class="text-gray-400!"
          maxlength="200"
        />
      </view>

      <!-- 提交按钮 -->
      <view
        class="w-full h-[44px] bg-[#cc3333] text-white rounded text-center leading-[44px] mt-8"
        @click="submit"
      >
        提交
      </view>
    </view>
  </PageContainer>
</template>

<style scoped>
.date-picker {
  width: 99%;
  border: 2rpx solid #a6a6a6;
  border-width: 1px;
  border-radius: 8rpx;
}
/deep/ .wd-picker__cell {
  width: 265rpx;
}
/deep/ .wd-picker__value {
  width: 215rpx;
  margin-right: 0rpx;
  font-size: 26rpx;
  text-align: center;
}
/deep/ .wd-picker__arrow {
  display: none;
}
</style>
