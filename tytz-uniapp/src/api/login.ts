import request from '@/utils/request'

/**
 * 获取验证码
 * @returns
 */
export const getAuthCaptcha = () => {
  return request<{
    captchaKey: string
    captchaBase64: string
  }>('/api/v1/auth/captcha', {
    method: 'GET',
  })
}

/**
 * 登录
 * @param data
 * @returns
 */
export const authLogin = (data: {
  username: string
  password: string
  captchaKey: string
  captchaCode: string
}) => {
  return request<{
    tokenType: string
    accessToken: string
    refreshToken: string
    expiresIn: number
  }>('/api/v1/auth/login', {
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
