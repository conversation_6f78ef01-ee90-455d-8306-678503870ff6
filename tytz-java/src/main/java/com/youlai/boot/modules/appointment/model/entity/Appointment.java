package com.youlai.boot.modules.appointment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.HandleEnum;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 约见管理实体类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@TableName("tsz_appointment")
@Data
public class Appointment extends BaseEntity {

    /**
     * 约见人名称
     */
    private String appointmentName;
    
    /**
     * 所属单位
     */
    private String appointmentUnit;
    
    /**
     * 联系方式
     */
    private String appointmentContact;
    
    /**
     * 被约见部委
     */
    private String appointmentDepartment;
    
    /**
     * 约见原因
     */
    private String appointmentReason;
    
    /**
     * 约见开始时间
     */
    private LocalDateTime appointmentStartTime;
    
    /**
     * 约见结束时间
     */
    private LocalDateTime appointmentEndTime;
    
    /**
     * 处理状态
     */
    private HandleEnum handleStatus;
    
    /**
     * 处理意见
     */
    private String handleComment;
    
    /**
     * 约见反馈
     */
    private String feedback;
    
    /**
     * 创建人ID
     */
    private Long createBy;
    
    /**
     * 更新人ID
     */
    private Long updateBy;
    
    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;

    /**
     * 商会会员id
     */
    private Long memberId;

    /**
     * 会员身份
     */
    private String memberRole;

} 