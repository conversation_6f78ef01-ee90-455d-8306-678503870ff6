package com.youlai.boot.modules.activity.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.ActivityTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * 活动管理分页查询对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Schema(description ="活动管理查询对象")
@Getter
@Setter
public class ActivityQuery extends BasePageQuery {
    
    @Schema(description = "活动名称")
    private String title;
    
    @Schema(description = "主办单位")
    private String department;

    @Schema(description = "活动类型")
    private ActivityTypeEnum activityType;
    
    @Schema(description = "开始时间")
    private String startTime;
    
    @Schema(description = "结束时间")
    private String endTime;
} 