<route lang="json5">
{
  style: {
    navigationBarTitleText: '问题详情',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { getProblemForm } from '@/api/problem/list'

const statusEnum = {
  WAIT: {
    label: '待处理',
    color: '#808080',
  },
  PASS: {
    label: '已采纳',
    color: '#8BC349',
  },
  REJECT: {
    label: '不予采纳',
    color: '#EF4142',
  },
}

const props = defineProps<{
  id: string
}>()

const detail = ref<Awaited<ReturnType<typeof getProblemForm>>>()

onMounted(async () => {
  const res = await getProblemForm(props.id)
  detail.value = res
})
</script>

<template>
  <PageContainer theme="page">
    <view class="p-4 bg-white">
      <view class="flex mb-3 items-center">
        <text class="text-sm text-gray-500 w-30 flex-shrink-0">营商环境类别</text>
        <text class="text-sm text-gray-800">{{ detail?.businessType }}</text>
      </view>
      <view class="flex mb-3 items-start">
        <text class="text-sm text-gray-500 w-30 flex-shrink-0">营商环境标题</text>
        <text class="text-sm text-gray-800">{{ detail?.title }}</text>
      </view>
      <view class="mb-3 items-start">
        <text class="text-sm text-gray-500 w-30 flex-shrink-0">营商环境内容描述</text>
        <view class="text-sm text-gray-800 leading-relaxed mt-2">{{ detail?.content }}</view>
      </view>

      <view class="mt-6 p-3 border-t border-gray-200 bg-#F8F8F9">
        <view class="flex mb-3 items-center">
          <text class="text-sm text-gray-500 w-30 flex-shrink-0">内容提交所属人:</text>
          <text class="text-sm text-gray-800">{{ detail?.memberName }}</text>
        </view>
        <view class="flex items-center">
          <text class="text-sm text-gray-500 w-30 flex-shrink-0">内容提交所属单位:</text>
          <text class="text-sm text-gray-800">{{ detail?.department }}</text>
        </view>
      </view>

      <view class="mt-6 pt-4 border-t border-gray-200">
        <view class="flex mb-3 items-center">
          <text class="text-sm text-gray-500 w-150rpx flex-shrink-0">采纳状态</text>
          <text
            class="text-sm font-bold"
            :style="{ color: statusEnum[detail?.adoptStatus]?.color }"
          >
            {{ statusEnum[detail?.adoptStatus]?.label || '-' }}
          </text>
        </view>
        <view class="flex items-start">
          <text class="text-sm text-gray-500 w-150rpx flex-shrink-0">采纳意见</text>
          <text class="text-sm text-gray-800">{{ detail?.adoptContent || '-' }}</text>
        </view>
      </view>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped></style>
