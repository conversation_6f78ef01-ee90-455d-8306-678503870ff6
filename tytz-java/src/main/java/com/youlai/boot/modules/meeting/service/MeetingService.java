package com.youlai.boot.modules.meeting.service;

import com.youlai.boot.modules.meeting.model.entity.Meeting;
import com.youlai.boot.modules.meeting.model.form.MeetingForm;
import com.youlai.boot.modules.meeting.model.query.MeetingQuery;
import com.youlai.boot.modules.meeting.model.vo.MeetingVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.system.model.bo.UserBO;

import java.util.List;

/**
 * 会议管理服务类
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
public interface MeetingService extends IService<Meeting> {

    /**
     * 会议管理分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<MeetingVO> getMeetingPage(MeetingQuery queryParams);

    /**
     * 获取会议管理表单数据
     *
     * @param id 会议ID
     * @return 表单数据
     */
    MeetingForm getMeetingFormData(Long id);

    /**
     * 新增会议
     *
     * @param formData 会议表单对象
     * @return 是否成功
     */
    boolean saveMeeting(MeetingForm formData);

    /**
     * 修改会议
     *
     * @param id 会议ID
     * @param formData 会议表单对象
     * @return 是否成功
     */
    boolean updateMeeting(Long id, MeetingForm formData);

    /**
     * 删除会议
     *
     * @param ids 会议ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteMeetings(String ids);

    /**
     * 根据用户ID列表获取UserBO列表
     *
     * @param userIds 用户ID列表
     * @return UserBO列表
     */
    List<UserBO> getUserBOListByIds(List<Long> userIds);

} 