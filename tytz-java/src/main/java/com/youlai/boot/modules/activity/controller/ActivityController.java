package com.youlai.boot.modules.activity.controller;

import com.youlai.boot.modules.activity.service.ActivityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.youlai.boot.modules.activity.model.form.ActivityForm;
import com.youlai.boot.modules.activity.model.query.ActivityQuery;
import com.youlai.boot.modules.activity.model.vo.ActivityVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * 活动管理前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Tag(name = "活动管理接口")
@RestController
@RequestMapping("/api/v1/activities")
@RequiredArgsConstructor
public class ActivityController {

    private final ActivityService activityService;

    @Operation(summary = "活动分页列表")
    @GetMapping("/page")
    // @PreAuthorize("@ss.hasPerm('modules.activity:activity:query')")
    public PageResult<ActivityVO> getActivityPage(ActivityQuery queryParams) {
        IPage<ActivityVO> result = activityService.getActivityPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增活动")
    @PostMapping
    // @PreAuthorize("@ss.hasPerm('modules.activity:activity:add')")
    public Result<Void> saveActivity(@RequestBody @Valid ActivityForm formData) {
        boolean result = activityService.saveActivity(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取活动表单数据")
    @GetMapping("/{id}/form")
    // @PreAuthorize("@ss.hasPerm('modules.activity:activity:edit')")
    public Result<ActivityForm> getActivityForm(
        @Parameter(description = "活动ID") @PathVariable Long id
    ) {
        ActivityForm formData = activityService.getActivityFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改活动")
    @PutMapping(value = "/{id}")
    // @PreAuthorize("@ss.hasPerm('modules.activity:activity:edit')")
    public Result<Void> updateActivity(
            @Parameter(description = "活动ID") @PathVariable Long id,
            @RequestBody @Validated ActivityForm formData
    ) {
        boolean result = activityService.updateActivity(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除活动")
    @DeleteMapping("/{ids}")
    // @PreAuthorize("@ss.hasPerm('modules.activity:activity:delete')")
    public Result<Void> deleteActivities(
        @Parameter(description = "活动ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = activityService.deleteActivities(ids);
        return Result.judge(result);
    }
} 