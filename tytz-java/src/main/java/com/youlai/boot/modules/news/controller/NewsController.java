package com.youlai.boot.modules.news.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.news.model.form.NewsForm;
import com.youlai.boot.modules.news.model.query.NewsQuery;
import com.youlai.boot.modules.news.model.vo.NewsVO;
import com.youlai.boot.modules.news.service.NewsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 新闻资讯控制器
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Tag(name = "新闻资讯管理", description = "新闻资讯管理接口")
@RestController
@RequestMapping("/api/v1/news")
@RequiredArgsConstructor
public class NewsController {

    private final NewsService newsService;

    @Operation(summary = "获取新闻资讯分页列表")
    @GetMapping("/page")
    public Result<IPage<NewsVO>> getNewsPage(
            @Parameter(description = "查询参数") NewsQuery queryParams
    ) {
        return Result.success(newsService.getNewsPage(queryParams));
    }

    @Operation(summary = "获取新闻资讯表单数据")
    @GetMapping("/{id}/form")
    public Result<NewsForm> getNewsForm(
            @Parameter(description = "新闻资讯ID") @PathVariable Long id
    ) {
        return Result.success(newsService.getNewsFormData(id));
    }

    @Operation(summary = "新增新闻资讯")
    @PostMapping
    public Result<Boolean> saveNews(
            @Parameter(description = "新闻资讯表单数据") @RequestBody NewsForm formData
    ) {
        return Result.success(newsService.saveNews(formData));
    }

    @Operation(summary = "修改新闻资讯")
    @PutMapping("/{id}")
    public Result<Boolean> updateNews(
            @Parameter(description = "新闻资讯ID") @PathVariable Long id,
            @Parameter(description = "新闻资讯表单数据") @RequestBody NewsForm formData
    ) {
        return Result.success(newsService.updateNews(id, formData));
    }

    @Operation(summary = "删除新闻资讯")
    @DeleteMapping("/{ids}")
    public Result<Boolean> deleteNews(
            @Parameter(description = "新闻资讯ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        return Result.success(newsService.deleteNews(ids));
    }
}