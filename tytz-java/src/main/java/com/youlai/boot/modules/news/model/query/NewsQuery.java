package com.youlai.boot.modules.news.model.query;

import java.time.LocalDateTime;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.PublishStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 新闻资讯查询对象
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Schema(description = "新闻资讯查询对象")
@Data
public class NewsQuery extends BasePageQuery {

    @Schema(description = "关键字(新闻标题)")
    private String keywords;

    @Schema(description = "发布状态（WAIT: 待审核, PUSH: 审核通过, CANCEL: 审核驳回）")
    private PublishStatusEnum publishStatus;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "创建时间结束")
    private String endTime;
}