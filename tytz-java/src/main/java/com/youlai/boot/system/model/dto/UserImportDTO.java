package com.youlai.boot.system.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用户导入对象
 *
 * <AUTHOR>
 * @since 2022/4/10
 */
@Data
public class UserImportDTO {

    @ExcelProperty(value = "用户名")
    private String username;

    @ExcelProperty(value = "昵称")
    private String nickname;

    @ExcelProperty(value = "性别")
    private String genderLabel;

    @ExcelProperty(value = "手机号码")
    private String mobile;

    @ExcelProperty(value = "邮箱")
    private String email;

    @ExcelProperty("角色")
    private String roleCodes;

    @ExcelProperty("部门")
    private String deptCode;

    @ExcelProperty("单位及职务")
    private String company;

    @ExcelProperty("地址")
    private String address;

}
