import request from "@/utils/request";

const API_PREFIX = "/api/v1/problems";

/**
 * 问题分页查询对象
 */
export interface LeaderInstructPageQuery extends PageQuery {
  /** 标题 */
  title?: string;

  /** 类别 */
  businessType?: string;

  /** 商会会员名称 */
  memberName?: string;

  /**所属部门 */
  department?: string;

  /**采纳状态 */
  adoptStatus?: string;

  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
/**问题分页对象 */
export interface LeaderInstructPageVO {
  /** 主键 */
  id?: number;

  /** 营商环境标题 */
  title?: string;

  /** 营商环境问题内容 */
  content?: string;

  /** 营商环境类别 */
  businessType?: string;

  /** 提交时间 */
  submitTime?: string;

  /** 商会会员名称 */
  memberName?: string;

  /** 所属部门 */
  department?: string;

  /** 采纳状态 */
  adoptStatus?: string;

  /** 采纳意见 */
  adoptContent?: string;

  /** 采纳人 */
  adoptBy?: string;

  /** 采纳时间 */
  adoptTime?: string;

  /** 批示状态 */
  instructionStatus?: string;

  /** 批示内容 */
  instructionContent?: string;

  /** 批示人 */
  instructionBy?: string;

  /** 批示时间 */
  instructionTime?: string;

  /** 领导批示 */
  leaderInstruction?: string;

  /**编辑领导批示 */
  leaderInstruct?: string;

  /**编辑领导批示内容 */
  instructContent?: string;

  /** 创建时间 */
  createTime?: string;
}
/**问题 */
export const leaderInstructPageApi = (queryParams: LeaderInstructPageQuery) => {
  return request<any, PageResult<LeaderInstructPageVO[]>>({
    url: `${API_PREFIX}/leaderInstruct/page`,
    method: "get",
    params: queryParams,
  });
};

export interface LeaderInstructVO {
  /**主键 */
  id?: string;

  /** 领导批示 */
  leaderInstruct?: string;

  /** 批示内容 */
  instructContent?: string;
}

/**领导批示 */
export const leaderInstructApi = (params: LeaderInstructVO) => {
  return request<any, LeaderInstructVO>({
    url: `${API_PREFIX}/instruct`,
    method: "PUT",
    data: params,
  });
};
