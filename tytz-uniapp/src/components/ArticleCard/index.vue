<script setup lang="ts">
export interface ArticleProps {
  /**
   * 标题
   */
  title?: string
  /**
   * 是否显示专题图标
   */
  imageUrl?: boolean
  /**
   * 发布者
   */
  publisher?: string
  /**
   * 评论次数
   */
  commentCount?: number
  /**
   * 发布时间
   */
  createTime?: string
}

const props = withDefaults(defineProps<ArticleProps>(), {
  title: '',
  imageUrl: false,
  publisher: '',
  commentCount: 0,
  createTime: '',
})
</script>

<template>
  <view class="card">
    <view class="card-item">
      <view class="card-item-title">{{ title }}</view>
      <view class="card-item-info">
        <view>{{ publisher }}</view>
        <!-- <view>{{ commentCount }} 评论</view> -->
        <view>{{ createTime }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.card-item-title {
  font-size: 35rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.card-item-info {
  display: flex;
  margin-top: 30rpx;
  view {
    font-size: 25rpx;
    color: #999;
    margin-right: 10px;
    margin-bottom: 30rpx;
  }
}
</style>
