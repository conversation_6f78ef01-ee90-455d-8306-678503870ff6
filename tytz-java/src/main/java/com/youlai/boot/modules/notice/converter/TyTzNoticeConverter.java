package com.youlai.boot.modules.notice.converter;

import org.mapstruct.Mapper;
import com.youlai.boot.modules.notice.model.entity.TyTzNotice;
import com.youlai.boot.modules.notice.model.form.TyTzNoticeForm;
import org.mapstruct.Mapping;

/**
 * 通知公告对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Mapper(componentModel = "spring")
public interface TyTzNoticeConverter {
    @Mapping(target = "attachments", ignore = true)
    TyTzNoticeForm toForm(TyTzNotice entity);

    @Mapping(target = "attachments", ignore = true)
    TyTzNotice toEntity(TyTzNoticeForm formData);
}