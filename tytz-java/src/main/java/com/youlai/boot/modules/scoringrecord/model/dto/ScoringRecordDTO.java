package com.youlai.boot.modules.scoringrecord.model.dto;

import com.youlai.boot.common.enums.ScoreTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评分记录数据传输对象
 */
@Data
public class ScoringRecordDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商会会员名称
     */
    private String memberName;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 评分类型
     */
    private ScoreTypeEnum scoringType;

    /**
     * 评分详细内容
     */
    private String scoringDetail;

    /**
     * 得分
     */
    private Long score;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 