import { computed, ref } from "vue";

import type { UseDisplayableDialogOptions } from "./type";

/**
 * EditableForm 组件的 Hook
 */
export function useDisplayableDialog<T extends Record<string, any>>(
  options: UseDisplayableDialogOptions
) {
  const visible = ref(false);
  const displayData = ref<Partial<T>>();

  const title = computed(() => {
    if (options.customTitle) return options.customTitle;
    return "详情";
  });

  const detailItemProps = computed(() => ({
    visible: visible.value,
    title: title.value,
    width: options.width || "800px",
    displayItems: options.displayItems,
    displayData: displayData.value,
    refreshService: (record: T) => Promise.resolve(record),
    onClose: () => {
      setTimeout(() => {
        displayData.value = undefined;
      }, 300);
      visible.value = false;
    },
  }));

  function showDisplayable(data: Partial<T>) {
    displayData.value = data;
    visible.value = true;
  }

  return {
    detailItemProps,
    showDisplayable,
  };
}
