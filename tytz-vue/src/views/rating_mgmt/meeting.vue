<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EditableItem, EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import displayableDialog from "@/components/EntityCrud/DisplayableDialog/index.vue";
import type { DisplayableItem } from "@/components/EntityCrud/DisplayableDialog/type";
import {
  meetingPageVO,
  meetingPageApi,
  meetingAddApi,
  meetingUpdateApi,
  meetingDeleteApi,
  meetingViewApi,
} from "@/api/rating/meeting";
//import UserAPI from "@/api/system/user";
import DeptAPI from "@/api/system/dept";
import dayjs from "dayjs";
const typeEnum = {
  MEMBER: "参加会员(代表)大会",
  PRESIDENT: "参加会长会议",
  DIRECTOR: "参加理事会会议",
};

const entityCrudRef = ref<any>(null);

const dialogVisible = ref(false);
const detailData = ref<meetingPageVO | null>(null);
const displayItems: DisplayableItem[] = [
  {
    label: "会议名称",
    prop: "title",
    type: "text",
  },
  {
    label: "会议类型",
    prop: "meetingType",
    type: "text",
    formatter: (value: string) => typeEnum[value as keyof typeof typeEnum],
  },
  {
    label: "参加人员",
    prop: "participants",
    type: "text",
  },
  {
    label: "会议时间",
    prop: "meetingTime",
    type: "date-range",
  },
  {
    label: "会议内容",
    prop: "content",
    type: "rich-text",
  },
  {
    label: "附件",
    prop: "attachments",
    type: "documents",
  },
];
const getFormItems = (): EditableItem<meetingPageVO> => {
  return {
    id: {
      type: "id",
    },
    title: {
      type: "input",
      label: "会议名称",
      required: true,
    },
    meetingType: {
      type: "select",
      label: "会议类型",
      options: getEnumOptions(typeEnum),
      required: true,
    },
    participants: {
      type: "select",
      label: "参加人员",
      required: true,
    },
    meetingTime: {
      type: "date-picker",
      label: "会议时间",
      required: true,
    },
    content: {
      type: "editor",
      label: "会议内容",
      required: true,
    },
    attachments: {
      type: "multiple-document",
      label: "附件",
      maxFileCount: 10,
      tip: "(注：最多上传10个文件，可上传.pdf、.jpg、.png、.xlsx、.docx、.xls、.doc格式)",
    },
  };
};

const config: EntityCrudProps<meetingPageVO> = {
  entityName: "meeting",
  displayName: "会议信息",
  hasIndexColumn: true,
  filterFormItems: {
    title: {
      type: "input",
      label: "会议名称",
    },
    meetingTime: {
      type: "date-range",
      label: "会议时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  rowOperations: [
    {
      label: "详情",
      type: "link",
      displayIndex: -1,
      actionService: async (row) => {
        const result: meetingPageVO = await meetingViewApi(row.id);
        result.meetingType = typeEnum[result.meetingType as keyof typeof typeEnum];
        result.meetingTime = [result.startTime, result.endTime];
        result.participants = result.participants
          .map(
            (participant: any) => participant.businessName + "(" + participant.businessMember + ")"
          )
          .join("，");
        detailData.value = result;
        dialogVisible.value = true;
      },
    },
  ],
  createButtonLabel: "新增",
  tableColumns: {
    title: "会议名称",
    meetingType: {
      label: "会议类型",
      formatter: (value: any) => {
        const key = value as keyof typeof typeEnum;
        return typeEnum[key];
      },
    },
    startTime: {
      label: "会议开始时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    endTime: {
      label: "会议结束时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
  },

  listFetchService: async (params) => {
    const result: any = await meetingPageApi(params);
    result.list.forEach((item: meetingPageVO) => {
      item.attachments = item.attachments ? JSON.parse(item.attachments) : [];
      item.meetingTime = [item.startTime, item.endTime];
      item.participants = item.participants
        ? JSON.parse(item.participants).map((participant: any) => participant.id.toString()) // 确保是字符串格式
        : [];
    });
    return Promise.resolve(result);
  },
  createFormItems: getFormItems(),
  createService: (newRecord: meetingPageVO) => {
    if (newRecord.meetingTime && newRecord.meetingTime.length >= 2) {
      const [startDate, endDate] = newRecord.meetingTime;
      newRecord.startTime = `${startDate} 00:00:00`;
      newRecord.endTime = `${endDate} 23:59:59`;
    }
    if (Array.isArray(newRecord.participants)) {
      newRecord.participants = newRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete newRecord.meetingTime;
    return meetingAddApi(newRecord);
  },
  useCreateFormItemsAsUpdate: true,
  updateService: (updateRecord: meetingPageVO) => {
    if (updateRecord.meetingTime && updateRecord.meetingTime.length >= 2) {
      const [startDate, endDate] = updateRecord.meetingTime;
      updateRecord.startTime = startDate.includes("T")
        ? startDate.replace("T", " ")
        : `${startDate} 00:00:00`;
      updateRecord.endTime = endDate.includes("T")
        ? endDate.replace("T", " ")
        : `${endDate} 23:59:59`;
    }
    if (Array.isArray(updateRecord.participants)) {
      updateRecord.participants = updateRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete updateRecord.meetingTime;
    return meetingUpdateApi(updateRecord.id, updateRecord);
  },
  deleteService: (row: meetingPageVO) => {
    return meetingDeleteApi(row.id);
  },
};
const entityCrudProps = defineEntityCrud(config);

const memberOptions = ref<{ label: string; value: string }[]>([]);

const getMemberOptions = async () => {
  await DeptAPI.getUsersByDeptId("5").then((res: any) => {
    if (Array.isArray(res)) {
      memberOptions.value = res.map((item: any) => {
        return {
          label: `${item.nickname}（${item.company ? item.company : "-"}）-${item.deptName}`,
          value: item.id,
        };
      });
    }
  });
};

const selectParticipants = ref<any>("");
const handleChangeSelect = (value: any) => {
  selectParticipants.value = value;
};
getMemberOptions();
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #participants="{ formData, props, change }">
        <el-select
          v-model="formData.participants"
          placeholder="请选择商会成员"
          multiple
          @change="handleChangeSelect($event)"
        >
          <el-option
            v-for="option in memberOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
    </EntityCrud>
    <displayableDialog
      v-model:visible="dialogVisible"
      :display-data="detailData"
      :display-items="displayItems"
      title="详情"
      width="800px"
    />
  </div>
</template>

<style lang="scss" scoped></style>
