import { defineSearchPage } from "@/components/SearchPage/hook";
import { useGlobalConfig } from "../config";
import type { EntityListProps } from "../type";
import type { TableColumnType } from "@/components/Table/type";

const globalConfig = useGlobalConfig();
const searchIsMapper: Record<string, "select" | "date-picker" | "select-remote"> = {
  select: "select",
  date: "date-picker",
  "date-range": "date-picker",
  "select-remote": "select-remote",
};

const hasSelectionColumn = (props: UseSearchableTableOptions<any>) => {
  return props.hasSelectionColumn ?? props.hasBatchService ?? false;
};

export type UseSearchableTableOptions<T> = EntityListProps<T> & {
  tableOperations?: any[];
  hasBatchService?: boolean;
  hasExternalRowOperation?: boolean;
};

export function useSearchableTable<T extends object>(props: UseSearchableTableOptions<T>) {
  return defineSearchPage({
    service: props.listFetchService,
    formItems:
      props.filterFormItems == null
        ? []
        : Object.keys(props.filterFormItems).map((key) => {
            const formItemType = props.filterFormItems[key].type as keyof typeof searchIsMapper;
            return {
              is: searchIsMapper[formItemType] ?? "input",
              formItem: {
                prop: key,
                label: props.filterFormItems[key].label,
              },
              props: {
                ...(props.filterFormItems[key].type === "select"
                  ? { options: props.filterFormItems[key].options }
                  : {}),
                ...(props.filterFormItems[key].type === "date"
                  ? {
                      type: "year",
                      format: "YYYY",
                      valueFormat: "YYYY",
                      defaultValue: new Date(),
                    }
                  : {}),
                ...(props.filterFormItems[key].type === "date-range" ? { type: "daterange" } : {}),
                ...(props.filterFormItems[key].type === "select-remote"
                  ? {
                      filterable: true,
                      remote: true,
                      reserveKeyword: true,
                      remoteShowSuffix: true,
                      remoteService: props.filterFormItems[key].remoteFetchService,
                    }
                  : {}),
                placeholder:
                  props.filterFormItems[key].placeholder ??
                  (props.filterFormItems[key].type === "input"
                    ? `请输入${props.filterFormItems[key].label}`
                    : `请选择${props.filterFormItems[key].label}`),
              },
              width: props.filterFormItems[key].type === "date-range" ? 320 : 250,
              ...(props.filterFormItems[key].type === "date-range"
                ? { dateRangeFields: props.filterFormItems[key].dateRangeFields }
                : {}),
            };
          }),
    operations: props.tableOperations,
    tableColumns: {
      ...(hasSelectionColumn(props) ? { selection: {} as TableColumnType<T>["selection"] } : {}),
      ...(props.hasIndexColumn ? { index: {} as TableColumnType<T>["index"] } : {}),
      ...(Object.entries(props.tableColumns).reduce(
        (acc, [key, value]) => ({
          ...acc,
          [key]:
            typeof value === "string"
              ? { label: value, emptyText: globalConfig.tableDataEmptyText }
              : {
                  ...value,
                  emptyText: value.emptyText ?? globalConfig.tableDataEmptyText,
                },
        }),
        {}
      ) as Partial<TableColumnType<T>>),
      ...(props.hasExternalRowOperation ||
      props.tableColumns.operations ||
      (props.rowOperations && props.rowOperations.length > 0)
        ? { operations: props.tableColumns.operations ?? ({} as TableColumnType<T>["operations"]) }
        : {}),
    } as TableColumnType<T>,
  });
}
