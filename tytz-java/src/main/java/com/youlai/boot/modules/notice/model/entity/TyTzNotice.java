package com.youlai.boot.modules.notice.model.entity;

import lombok.Getter;
import lombok.Setter;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.PublishStatusEnum;

/**
 * 通知公告实体对象
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Getter
@Setter
@TableName("tsz_notice")
public class TyTzNotice extends BaseEntity {

    /**
     * 通知标题
     */
    private String title;
    /**
     * 通知内容
     */
    private String content;
    /**
     * 发布状态
     */
    private PublishStatusEnum publishStatus;
    /**
     * 附件JSON数据，包含文件名、路径等信息
     */
    private String attachments;
    /**
     * 创建人ID
     */
    private Long createBy;
    /**
     * 更新人ID
     */
    private Long updateBy;
    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
}
