<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import {
  noticePageVO,
  noticePageApi,
  noticeAddApi,
  noticeUpdateApi,
  noticeViewApi,
  noticeDeleteApi,
} from "@/api/news/notice";

const statusOptionsEnum = {
  WAIT: "待发布",
  PUSH: "已发布",
  CANCEL: "取消发布",
};

const statusEnum = {
  PUSH: "已发布",
  WAIT: "暂存",
};

const entityCrudRef = ref<any>(null);

const config: EntityCrudProps<noticePageVO> = {
  entityName: "notice",
  displayName: "通知公告",
  hasIndexColumn: true,
  filterFormItems: {
    keywords: {
      type: "input",
      label: "标题",
    },
    publishStatus: {
      type: "select",
      label: "发布状态",
      options: getEnumOptions(statusOptionsEnum),
    },
    createTime: {
      type: "date-range",
      label: "发布时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  createButtonLabel: "新增",
  tableColumns: {
    title: "标题",
    createTime: "发布时间",
    createBy: "发布者",
    publishStatus: "发布状态",
    operations: {
      label: "操作",
    },
  },
  createFormItems: {
    id: {
      type: "id",
    },
    title: {
      type: "input",
      label: "标题",
      required: true,
    },
    content: {
      type: "editor",
      label: "内容",
      required: true,
    },
    attachments: {
      type: "multiple-document",
      label: "上传附件",
      maxFileCount: 10,
      tip: "上传附件,单个文件大小不超过20MB最多可上传 10 个文件,且格式为image/*、xls、xlsx、pdf、doc、 docx",
    },
    publishStatus: {
      type: "radio",
      label: "状态",
      options: getEnumOptions(statusEnum),
      required: true,
    },
  },
  listFetchService: async (params) => {
    const result: any = await noticePageApi(params);
    result.list.forEach((item: any) => {
      item.attachments = item.attachments ? JSON.parse(item.attachments) : [];
    });
    return Promise.resolve(result);
  },
  createService: (newRecord: noticePageVO) => {
    return noticeAddApi(newRecord);
  },
  useCreateFormItemsAsUpdate: true,
  canUpdate: (row) => row.publishStatus !== "PUSH",
  updateService: (updateRecord: noticePageVO) => {
    return noticeUpdateApi(updateRecord.id, updateRecord);
  },
  detailButtonLabel: "查看",
  useCreateFormItemsAsDetail: true,
  canViewDetail: (row) => row.publishStatus == "PUSH",
  detailFetchService: (row) => {
    return noticeViewApi(row.id);
  },
  publishButtonLabel: "发布",
  canPublish: (row) => row.publishStatus == "WAIT",
  publishService: (record: noticePageVO) => {
    const params = {
      publishStatus: "PUSH",
      id: record.id,
      title: record.title,
    };
    record.publishStatus = "PUSH";
    return noticeUpdateApi(record.id, params);
  },
  unpublishButtonLabel: "取消发布",
  canUnpublish: (row) => row.publishStatus == "PUSH",
  unpublishService: (record: noticePageVO) => {
    const params = {
      publishStatus: "CANCEL",
      id: record.id,
      title: record.title,
    };
    return noticeUpdateApi(record.id, params);
  },
  canDelete: (row) => row.publishStatus == "CANCEL",
  deleteService: (row: noticePageVO) => {
    return noticeDeleteApi(row.id);
  },
};
const entityCrudProps = defineEntityCrud(config);
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #tablePublishStatus="{ row }: { row: noticePageVO }">
        <el-tag
          :type="
            row.publishStatus === 'WAIT'
              ? 'warning'
              : row.publishStatus === 'CANCEL'
                ? 'danger'
                : 'success'
          "
        >
          {{
            row.publishStatus
              ? statusOptionsEnum[row.publishStatus as keyof typeof statusOptionsEnum]
              : "-"
          }}
        </el-tag>
      </template>
    </EntityCrud>
  </div>
</template>

<style lang="scss" scoped></style>
