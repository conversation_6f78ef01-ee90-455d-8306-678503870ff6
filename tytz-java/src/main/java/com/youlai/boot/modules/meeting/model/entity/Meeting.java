package com.youlai.boot.modules.meeting.model.entity;

import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.MeetingTypeEnum;

/**
 * 会议管理实体对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@TableName("tsz_meeting")
public class Meeting extends BaseEntity {

    /**
     * 会议名称
     */
    private String title;

    /**
     * 会议类型
     */
    private MeetingTypeEnum meetingType;
    
    /**
     * 组织者
     */
    private String department;
    
    /**
     * 参会人员JSON数据
     */
    private String participants;
    
    /**
     * 会议开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 会议结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 会议内容
     */
    private String content;
    
    /**
     * 附件JSON数据，包含文件名、路径等信息
     */
    private String attachments;
    
    /**
     * 会议状态（0: 未开始, 1: 进行中, 2: 已结束）
     */
    //private Integer meetingStatus;
    
    /**
     * 创建人ID
     */
    private Long createBy;
    
    /**
     * 更新人ID
     */
    private Long updateBy;
    
    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
} 