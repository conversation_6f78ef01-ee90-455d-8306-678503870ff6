package com.youlai.boot.modules.appointment.controller;

import com.youlai.boot.common.enums.AppoinitMentEnum;
import com.youlai.boot.common.enums.HandleEnum;
import com.youlai.boot.modules.appointment.model.dto.AppointmentDTO;
import com.youlai.boot.modules.appointment.model.query.MyAppointmentQuery;
import com.youlai.boot.modules.appointment.service.AppointmentService;
import com.youlai.boot.modules.appointment.model.form.AppointmentForm;
import com.youlai.boot.modules.appointment.model.query.AppointmentQuery;
import com.youlai.boot.modules.appointment.model.vo.AppointmentVO;
import com.youlai.boot.modules.appointment.model.vo.AppointmentExcelVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 约见管理前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Tag(name = "约见管理接口")
@RestController
@RequestMapping("/api/v1/appointments")
@RequiredArgsConstructor
public class AppointmentController {

    private final AppointmentService appointmentService;

    @Operation(summary = "约见管理分页列表")
    @GetMapping("/page")
    public PageResult<AppointmentVO> getAppointmentPage(AppointmentQuery queryParams) {
        IPage<AppointmentVO> result = appointmentService.getAppointmentPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增约见申请")
    @PostMapping
    public Result<Void> saveAppointment(@RequestBody @Valid AppointmentForm formData) {
        boolean result = appointmentService.saveAppointment(formData);
        return Result.judge(result);
    }

    @GetMapping("/my/page")
    @Operation(summary = "获得我的提交的约见申请")
    public PageResult<AppointmentVO> getMyAppointmentPage(MyAppointmentQuery queryParams) {
        IPage<AppointmentVO> result = appointmentService.getMyAppointmentPage(queryParams);
        // 遍历VO列表设置状态
        result.getRecords().forEach(vo -> {
            if (vo.getHandleStatus() == null) {
                vo.setAppointmentStatus(AppoinitMentEnum.WAIT);
            } else if (vo.getHandleStatus().equals(HandleEnum.PASS)) {
                LocalDateTime now = LocalDateTime.now();
                if (now.isBefore(vo.getAppointmentStartTime())) {
                    vo.setAppointmentStatus(AppoinitMentEnum.WAIT_START);
                } else if (now.isAfter(vo.getAppointmentStartTime()) &&
                        now.isBefore(vo.getAppointmentEndTime())) {
                    vo.setAppointmentStatus(AppoinitMentEnum.MEETING);
                } else if (now.isAfter(vo.getAppointmentEndTime())) {
                    vo.setAppointmentStatus(AppoinitMentEnum.END);
                }
            } else if (vo.getHandleStatus().equals(HandleEnum.REJECT)) {
                vo.setAppointmentStatus(AppoinitMentEnum.REJECT);
            }
        });
        return PageResult.success(result);
    }

    @Operation(summary = "获取约见申请表单数据")
    @GetMapping("/{id}/form")
    public Result<AppointmentForm> getAppointmentForm(
        @Parameter(description = "约见管理ID") @PathVariable Long id
    ) {
        AppointmentForm formData = appointmentService.getAppointmentFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改约见申请")
    @PutMapping(value = "/{id}")
    public Result<Void> updateAppointment(
            @Parameter(description = "约见管理ID") @PathVariable Long id,
            @RequestBody @Validated AppointmentForm formData
    ) {
        boolean result = appointmentService.updateAppointment(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除约见申请")
    @DeleteMapping("/{ids}")
    public Result<Void> deleteAppointments(
        @Parameter(description = "约见管理ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = appointmentService.deleteAppointments(ids);
        return Result.judge(result);
    }

    @Operation(summary = "处理约见申请")
    @PutMapping("/{id}/handle")
    public Result<Void> handleAppointment(
        @Parameter(description = "约见管理ID") @PathVariable Long id,
        @RequestBody @Validated AppointmentDTO dto
    ) {
        boolean result = appointmentService.handleAppointment(id, dto);
        return Result.judge(result);
    }

    @Operation(summary = "提交约见反馈")
    @PutMapping("/{id}/feedback")
    public Result<Void> submitFeedback(
        @Parameter(description = "约见管理ID") @PathVariable Long id,
        @RequestBody AppointmentDTO dto
    ) {
        boolean result = appointmentService.submitFeedback(id, dto.getFeedback());
        return Result.judge(result);
    }

    /**
     * 导出约见管理数据
     */
    @Operation(summary = "导出约见管理数据")
    @PostMapping("/export")
    public void exportAppointments(HttpServletResponse response, @RequestBody AppointmentQuery queryParams) throws IOException {
        // 设置响应头
        String fileName = "约见管理数据.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        // 获取数据
        List<AppointmentVO> list = appointmentService.getExportList(queryParams);
        List<AppointmentExcelVO> excelVOList = list.stream()
                .map(item -> {
                    AppointmentExcelVO excelVO = new AppointmentExcelVO();
                    BeanUtils.copyProperties(item, excelVO);
                    excelVO.setIndex(Long.valueOf(list.indexOf(item) + 1));
                    return excelVO;
                }).collect(Collectors.toList());

        // 导出数据
        EasyExcel.write(response.getOutputStream(), AppointmentExcelVO.class)
                .sheet("约见管理数据")
                .doWrite(excelVOList);
    }

    @Operation(summary = "获取指定状态的约见列表")
    @GetMapping("/status/{status}")
    public Result<List<AppointmentVO>> getAppointmentListByStatus(
            @Parameter(description = "约见状态") @PathVariable AppoinitMentEnum status) {
        List<AppointmentVO> list = appointmentService.getAppointmentListByStatus(status);
        return Result.success(list);
    }
}