package com.youlai.boot.modules.problem.converter;

import com.youlai.boot.modules.problem.model.entity.Problem;
import com.youlai.boot.modules.problem.model.form.ProblemForm;
import com.youlai.boot.modules.problem.model.vo.ProblemVO;
import org.mapstruct.Mapper;

/**
 * 营商环境问题对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Mapper(componentModel = "spring")
public interface ProblemConverter {

    ProblemForm toForm(Problem entity);

    Problem toEntity(ProblemForm form);

    ProblemVO toVO(Problem entity);
} 