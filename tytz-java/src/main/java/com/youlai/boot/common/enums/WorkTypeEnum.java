package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 年度重点工作类型枚举
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
public enum WorkTypeEnum implements IBaseEnum<String> {

    PROJECT_SERVICE("PROJECT_SERVICE", "助力项目建设服务，参加引进外资活动"),
    BUSINESS_ENVIRONMENT("BUSINESS_ENVIRONMENT", "助推创一流营商环境"),
    OTHER_TASKS("OTHER_TASKS", "完成区商会交办的其他任务");

    private final String value;

    private final String label;

    WorkTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
} 