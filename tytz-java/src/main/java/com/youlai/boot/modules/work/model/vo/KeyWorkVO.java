package com.youlai.boot.modules.work.model.vo;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.youlai.boot.common.enums.WorkTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 年度重点工作视图对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Schema(description = "年度重点工作视图对象")
public class KeyWorkVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "重点工作ID")
    private Long id;
    
    @Schema(description = "年度")
    private Integer year;
    
    @Schema(description = "工作名称")
    private String workName;

    @Schema(description = "工作类型")
    private WorkTypeEnum workType;
    
    @Schema(description = "负责部门")
    private String department;
    
    @Schema(description = "负责人员JSON数据")
    private String participants;
    
    @Schema(description = "开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "结束时间")
    private LocalDateTime endTime;
    
    @Schema(description = "工作内容")
    private String workContent;
    
    @Schema(description = "附件JSON数据")
    private String attachments;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 