import { camelCase, kebabCase, merge } from "lodash-es";
import { computed, useAttrs, useSlots } from "vue";

import type { SearchPageProps } from "./type";

/**
 * 定义 SearchPage 组件
 * @param props
 * @returns
 */
/* export function defineSearchPage<T extends object>(props: SearchPageProps<T>) {
  return props;
} */
export function defineSearchPage<R extends object, T extends R>(props: SearchPageProps<R, T>) {
  return props;
}
/**
 * 根据 attrs 生成子组件的attrs
 */
export function useChildrenAttrs() {
  const attrs = useAttrs();
  function getChildrenAttrs(key: string, value: any, prefix = "") {
    const kebabCaseKey = kebabCase(key);
    const index = kebabCaseKey.indexOf("-");
    if (index !== -1) {
      const name = kebabCaseKey.slice(0, index);
      const propKey = kebabCaseKey.slice(index + 1);
      return {
        [name]: {
          [camelCase(prefix + propKey)]: value,
        },
      };
    } else {
      return {};
    }
  }
  return computed(() => {
    const list = Object.entries(attrs).map(([key, value]) => {
      if (key.startsWith("on")) {
        return getChildrenAttrs(key.slice(2), value, "on-");
      }
      return getChildrenAttrs(key, value);
    });
    return list.reduce((acc, cur) => merge(acc, cur), {});
  });
}

/**
 * 根据 slots 生成子组件的slots
 */
export function useChildrenSlots() {
  const slots = useSlots();
  return computed(() => {
    const list = Object.entries(slots).map(([key]) => {
      const kebabCaseKey = kebabCase(key);
      const index = kebabCaseKey.indexOf("-");
      if (index !== -1) {
        const name = kebabCaseKey.slice(0, index);
        const slotKey = kebabCaseKey.slice(index + 1);
        return {
          [name]: {
            [camelCase(slotKey)]: key,
          },
        };
      }
    });
    return list.reduce((acc, cur) => merge(acc, cur), {}) || {};
  });
}
