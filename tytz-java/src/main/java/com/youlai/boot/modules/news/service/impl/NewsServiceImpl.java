package com.youlai.boot.modules.news.service.impl;

import cn.hutool.json.JSONUtil;

import com.youlai.boot.common.enums.PublishStatusEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.modules.news.mapper.NewsMapper;
import com.youlai.boot.modules.news.service.NewsService;
import com.youlai.boot.modules.news.model.entity.News;
import com.youlai.boot.modules.news.model.form.NewsForm;
import com.youlai.boot.modules.news.model.form.NewsForm.FileDTO;
import com.youlai.boot.modules.news.model.query.NewsQuery;
import com.youlai.boot.modules.news.model.vo.NewsVO;
import com.youlai.boot.modules.news.converter.NewsConverter;
import com.youlai.boot.system.mapper.UserMapper;
import com.youlai.boot.system.model.bo.UserBO;

import java.util.Arrays;
import java.util.List;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

/**
 * 新闻资讯服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Service
@RequiredArgsConstructor
public class NewsServiceImpl extends ServiceImpl<NewsMapper, News> implements NewsService {

    private final NewsConverter newsConverter;
    private final UserMapper userMapper;

    /**
    * 获取新闻资讯分页列表
    *
    * @param queryParams 查询参数
    * @return {@link IPage<NewsVO>} 新闻资讯分页列表
    */
    @Override
    public IPage<NewsVO> getNewsPage(NewsQuery queryParams) {
        Page<NewsVO> pageVO = this.baseMapper.getNewsPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );
        return pageVO;
    }

    /**
     * 获取新闻资讯表单数据
     *
     * @param id 新闻资讯ID
     * @return 新闻资讯表单对象
     */
    @Override
    public NewsForm getNewsFormData(Long id) {
        News entity = this.getById(id);
        List<FileDTO> list = JSONUtil.toList(entity.getAttachments(), FileDTO.class);
        NewsForm form = newsConverter.toForm(entity);
        form.setAttachments(list);

        // 根据createBy查询用户昵称
        if (entity.getCreateBy() != null) {
            UserBO userBO = userMapper.getUserProfile(entity.getCreateBy());
            if (userBO != null) {
                form.setCreateBy(userBO.getNickname());
            }
        }

        return form;
    }

    /**
     * 新增新闻资讯
     *
     * @param formData 新闻资讯表单对象
     * @return 是否成功
     */
    @Override
    public boolean saveNews(NewsForm formData) {
        News entity = newsConverter.toEntity(formData);
        if(formData.getPublishStatus() == null) {
            entity.setPublishStatus(PublishStatusEnum.WAIT);
        }
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setCreateBy(SecurityUtils.getUserId());
        return this.save(entity);
    }

    /**
     * 更新新闻资讯
     *
     * @param id   新闻资讯ID
     * @param formData 新闻资讯表单对象
     * @return 是否成功
     */
    @Override
    public boolean updateNews(Long id, NewsForm formData) {
        News entity = newsConverter.toEntity(formData);
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setUpdateBy(SecurityUtils.getUserId());
        return this.updateById(entity);
    }

    /**
     * 删除新闻资讯
     *
     * @param ids 新闻资讯ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    public boolean deleteNews(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的新闻资讯数据为空");
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();
        return this.removeByIds(idList);
    }
}