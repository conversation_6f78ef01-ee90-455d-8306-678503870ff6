<script setup lang="ts">
import { omit } from "lodash-es";
import { computed } from "vue";

import { useDictStoreHook } from "@/store/modules/dict";

import { defaultProps } from "./config";
import type { SelectProps } from "./type";
const dictStore = useDictStoreHook();

const props = withDefaults(defineProps<SelectProps>(), defaultProps);
const ElSelectProps = computed(() => omit(props, ["columns", "modelValue", "options"]));
const model = defineModel<string>();
</script>

<template>
  <ElSelect v-bind="ElSelectProps" v-model="model">
    <ElOption
      v-for="item in dictType ? dictStore.getDictItemToList(dictType) : options"
      :key="item.label"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    />
  </ElSelect>
</template>

<style scoped></style>
