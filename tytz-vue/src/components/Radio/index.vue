<script setup lang="ts">
import { useDictStoreHook } from "@/store/modules/dict";

import type { RadioProps } from "./type";
const dictStore = useDictStoreHook();
const props = withDefaults(defineProps<RadioProps>(), {
  options: () => [],
});

defineOptions({
  name: "Radio",
});
</script>

<template>
  <el-radio-group class="ml-4">
    <el-radio
      v-for="item in props.dictType ? dictStore.getDictItemToList(props.dictType) : props.options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    />
  </el-radio-group>
</template>

<style scoped></style>
