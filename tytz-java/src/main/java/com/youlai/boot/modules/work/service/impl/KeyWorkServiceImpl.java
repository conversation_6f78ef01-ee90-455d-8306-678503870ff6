package com.youlai.boot.modules.work.service.impl;

import cn.hutool.json.JSONUtil;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import com.youlai.boot.common.enums.WorkTypeEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.system.mapper.UserMapper;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.modules.work.mapper.KeyWorkMapper;
import com.youlai.boot.modules.work.service.KeyWorkService;
import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.youlai.boot.modules.work.model.form.KeyWorkForm;
import com.youlai.boot.modules.work.model.query.KeyWorkQuery;
import com.youlai.boot.modules.work.model.vo.KeyWorkVO;
import com.youlai.boot.modules.work.converter.KeyWorkConverter;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 年度重点工作服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
@RequiredArgsConstructor
public class KeyWorkServiceImpl extends ServiceImpl<KeyWorkMapper, KeyWork> implements KeyWorkService {

    private final KeyWorkConverter keyWorkConverter;
    private final UserService userService;
    private final ScoringRecordMapper scoringRecordMapper;
    @Autowired
    private UserMapper userMapper;
    /**
     * 获取年度重点工作分页列表
     *
     * @param queryParams 查询参数
     * @return 重点工作分页列表
     */
    @Override
    public IPage<KeyWorkVO> getKeyWorkPage(KeyWorkQuery queryParams) {
        Page<KeyWorkVO> pageVO = this.baseMapper.getKeyWorkPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );
        return pageVO;
    }

    /**
     * 获取年度重点工作表单数据
     *
     * @param id 重点工作ID
     * @return 表单数据
     */
    @Override
    public KeyWorkForm getKeyWorkFormData(Long id) {
        KeyWork entity = this.getById(id);
        Assert.notNull(entity, "重点工作不存在");
        // 解析参与人员ID数组
        List<Map> participantMaps = JSONUtil.toList(entity.getParticipants(), Map.class);
        List<Long> participantIds = participantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 查询用户信息，使用UserBO对象
        List<UserBO> userBOs = getUserBOListByIds(participantIds);

        // 转换为参与者ID DTO
        List<KeyWorkForm.ParticipantDTO> participantDTOs = userBOs.stream()
                .map(userBO -> {
                    return KeyWorkForm.ParticipantDTO.builder()
                            .id(userBO.getId())
                            .businessName(userBO.getNickname())
                            .businessMember(userBO.getCompany() + " - " + userBO.getDeptName())
                            .build();
                })
                .collect(Collectors.toList());

        List<KeyWorkForm.FileDTO> attachments = JSONUtil.toList(entity.getAttachments(), KeyWorkForm.FileDTO.class);

        KeyWorkForm form = keyWorkConverter.toForm(entity);
        form.setParticipants(participantDTOs);
        form.setAttachments(attachments);
        return form;
    }

    /**
     * 新增年度重点工作
     *
     * @param formData 重点工作表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveKeyWork(KeyWorkForm formData) {
        KeyWork entity = keyWorkConverter.toEntity(formData);
        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());
        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setCreateBy(SecurityUtils.getUserId());
        entity.setCreateTime(LocalDateTime.now());

        // 先保存工作信息，获取工作ID
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            return false;
        }

        // 使用保存后的工作ID创建评分记录
        Long workId = entity.getId();

        // 为每个参与者创建评分记录
        formData.getParticipants().stream().forEach(participant -> {
            ScoringRecord scoringRecord = new ScoringRecord();
            scoringRecord.setMemberId(participant.getId());
            scoringRecord.setWorkId(workId); // 设置工作ID

            // 根据用户ID查询用户信息
            UserBO userBO = userMapper.getUserProfile(participant.getId());
            if (userBO != null) {
                // 设置会员名称和部门
                scoringRecord.setMemberName(userBO.getNickname());
                scoringRecord.setDepartment(userBO.getCompany());
            } else {
                // 如果查询不到用户信息，则使用参与者对象中的信息
                scoringRecord.setMemberName(participant.getBusinessName());
                scoringRecord.setDepartment(participant.getBusinessMember());
            }

            scoringRecord.setScoringType(ScoreTypeEnum.KEY_WORK);
            scoringRecord.setScoringDetail(formData.getWorkType().getLabel());

            // 根据工作类型设置分数
            if (WorkTypeEnum.PROJECT_SERVICE.equals(formData.getWorkType())) {
                // 助力项目建设服务，参加引进外资活动得3分
                scoringRecord.setScore(3L);
            } else if (WorkTypeEnum.BUSINESS_ENVIRONMENT.equals(formData.getWorkType())) {
                // 助推创一流营商环境得3分
                scoringRecord.setScore(3L);
            } else if (WorkTypeEnum.OTHER_TASKS.equals(formData.getWorkType())) {
                // 完成区商会交办的其他任务得3分
                scoringRecord.setScore(3L);
            }

            scoringRecord.setCreateBy(SecurityUtils.getUserId());
            scoringRecord.setCreateTime(LocalDateTime.now());
            scoringRecord.setYear(String.valueOf(formData.getYear()));
            scoringRecordMapper.insert(scoringRecord);
        });

        return true;
    }

    /**
     * 更新年度重点工作
     *
     * @param id 重点工作ID
     * @param formData 重点工作表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateKeyWork(Long id, KeyWorkForm formData) {
        // 获取原工作信息
        KeyWork oldWork = this.getById(id);
        Assert.notNull(oldWork, "年度重点工作不存在");

        KeyWork entity = keyWorkConverter.toEntity(formData);
        entity.setId(id);
        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());
        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setUpdateBy(SecurityUtils.getUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 解析原参与人员ID数组
        List<Map> oldParticipantMaps = JSONUtil.toList(oldWork.getParticipants(), Map.class);
        List<Long> oldParticipantIds = oldParticipantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 解析新参与人员ID数组
        List<Long> newParticipantIds = formData.getParticipants().stream()
                .map(participant -> participant.getId())
                .collect(Collectors.toList());

        // 如果工作类型发生变化或者参与人员发生变化，需要更新评分记录
        boolean workTypeChanged = !oldWork.getWorkType().equals(entity.getWorkType());
        boolean participantsChanged = !oldParticipantIds.equals(newParticipantIds);

        if (workTypeChanged || participantsChanged) {
            // 删除原有的评分记录
            scoringRecordMapper.deleteByWorkId(id);

            // 重新创建评分记录
            formData.getParticipants().stream().forEach(participant -> {
                ScoringRecord scoringRecord = new ScoringRecord();
                scoringRecord.setMemberId(participant.getId());
                scoringRecord.setWorkId(id); // 设置工作ID

                // 根据用户ID查询用户信息
                UserBO userBO = userMapper.getUserProfile(participant.getId());
                if (userBO != null) {
                    // 设置会员名称和部门
                    scoringRecord.setMemberName(userBO.getNickname());
                    scoringRecord.setDepartment(userBO.getCompany());
                } else {
                    // 如果查询不到用户信息，则使用参与者对象中的信息
                    scoringRecord.setMemberName(participant.getBusinessName());
                    scoringRecord.setDepartment(participant.getBusinessMember());
                }

                scoringRecord.setScoringType(ScoreTypeEnum.KEY_WORK);
                scoringRecord.setScoringDetail(formData.getWorkType().getLabel());

                // 根据工作类型设置分数
                if (WorkTypeEnum.PROJECT_SERVICE.equals(formData.getWorkType())) {
                    // 助力项目建设服务，参加引进外资活动得5分
                    scoringRecord.setScore(3L);
                } else if (WorkTypeEnum.BUSINESS_ENVIRONMENT.equals(formData.getWorkType())) {
                    // 助推创一流营商环境得3分
                    scoringRecord.setScore(3L);
                } else if (WorkTypeEnum.OTHER_TASKS.equals(formData.getWorkType())) {
                    // 完成区商会交办的其他任务得2分
                    scoringRecord.setScore(3L);
                }

                scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                scoringRecord.setUpdateTime(LocalDateTime.now());
                scoringRecord.setYear(String.valueOf(formData.getYear()));
                scoringRecordMapper.insert(scoringRecord);
            });
        }

        return this.updateById(entity);
    }

    /**
     * 删除年度重点工作
     *
     * @param ids 重点工作ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteKeyWorks(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的重点工作数据为空");
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();

        // 删除相关的评分记录
        for (Long id : idList) {
            scoringRecordMapper.deleteByWorkId(id);
        }

        return this.removeByIds(idList);
    }

    /**
     * 根据用户ID列表获取UserBO列表
     *
     * @param userIds 用户ID列表
     * @return UserBO列表
     */
    @Override
    public List<UserBO> getUserBOListByIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 直接使用UserService的listByIds方法获取User实体
        List<User> users = userService.listByIds(userIds);

        // 使用UserMapper获取用户信息，包含部门名称等
        List<UserBO> userBOs = new ArrayList<>();
        for (User user : users) {
            // 获取用户个人信息，包含部门名称
            UserBO userBO = userMapper.getUserProfile(user.getId());
            if (userBO != null) {
                userBOs.add(userBO);
            }
        }

        return userBOs;
    }
}