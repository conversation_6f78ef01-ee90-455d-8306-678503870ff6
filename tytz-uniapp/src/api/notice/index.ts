import request from '@/utils/request'

/**
 * DataProblemVO
 */
export interface DataNoticeVO {
  list?: NoticeVO[]
  total?: number
}

export interface NoticeVO {
  /** 主键 */
  id: string

  /** 通知标题 */
  title?: string

  /** 内容 */
  content?: string

  /** 发布状态 */
  publishStatus?: string

  /** 附件 */
  attachments?: string

  /** 创建时间 */
  createTime?: string

  /** 更新时间 */
  updateTime?: string

  /** 发布者 */
  createBy?: string
}

/**
 * 获取通知公告列表
 * @param params
 * @returns
 */
export const getNoticePage = (params: {
  pageNum: number
  pageSize: number
  publishStatus: string
}) => {
  return request<DataNoticeVO>('/api/v1/tytzNotices/page', {
    method: 'GET',
    data: params,
  })
}

/**
 * 获取通知公告详情
 * @param id
 * @returns
 */
export const getNoticeDetail = (id: string) => {
  return request<NoticeVO>(`/api/v1/tytzNotices/${id}/form`, {
    method: 'GET',
  })
}
