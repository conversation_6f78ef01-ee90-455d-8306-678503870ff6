package com.youlai.boot.modules.businessmaterials.service;

import com.youlai.boot.modules.businessmaterials.model.entity.BusinessMaterials;
import com.youlai.boot.modules.businessmaterials.model.form.BusinessMaterialsForm;
import com.youlai.boot.modules.businessmaterials.model.query.BusinessMaterialsQuery;
import com.youlai.boot.modules.businessmaterials.model.vo.BusinessMaterialsVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 营商材料服务类
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
public interface BusinessMaterialsService extends IService<BusinessMaterials> {

    /**
     *营商材料分页列表
     *
     * @return
     */
    IPage<BusinessMaterialsVO> getBusinessMaterialsPage(BusinessMaterialsQuery queryParams);

    /**
     * 获取营商材料表单数据
     *
     * @param id 营商材料ID
     * @return
     */
     BusinessMaterialsForm getBusinessMaterialsFormData(Long id);

    /**
     * 新增营商材料
     *
     * @param formData 营商材料表单对象
     * @return
     */
    boolean saveBusinessMaterials(BusinessMaterialsForm formData);

    /**
     * 修改营商材料
     *
     * @param id   营商材料ID
     * @param formData 营商材料表单对象
     * @return
     */
    boolean updateBusinessMaterials(Long id, BusinessMaterialsForm formData);

    /**
     * 删除营商材料
     *
     * @param ids 营商材料ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteBusinessMaterialss(String ids);

}
