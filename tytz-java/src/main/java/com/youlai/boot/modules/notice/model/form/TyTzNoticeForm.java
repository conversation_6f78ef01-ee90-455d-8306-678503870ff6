package com.youlai.boot.modules.notice.model.form;

import java.io.Serial;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

import com.youlai.boot.common.enums.PublishStatusEnum;

import jakarta.validation.constraints.*;

/**
 * 通知公告表单对象
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Getter
@Setter
@Schema(description = "通知公告表单对象")
public class TyTzNoticeForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "通知标题")
    @NotBlank(message = "通知标题不能为空")
    @Size(max=100, message="通知标题长度不能超过100个字符")
    private String title;

    @Schema(description = "通知内容")
    @Size(max=65535, message="通知内容长度不能超过65535个字符")
    private String content;

    @Schema(description = "发布状态")
    private PublishStatusEnum publishStatus;

    @Schema(description = "附件JSON数据，包含文件名、路径等信息")
    @Size(max=65535, message="附件JSON数据，包含文件名、路径等信息长度不能超过65535个字符")
    private List<FileDTO> attachments;

    @Schema(description = "发布时间")
    private String createTime;

    @Schema(description = "创建人")
    private String createBy;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileDTO {

        @Schema(description = "文件名称")
        String name;

        @Schema(description = "文件URL")
        String url;

    }




}
