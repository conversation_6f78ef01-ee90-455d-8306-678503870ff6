package com.youlai.boot.modules.opinion.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.opinion.model.entity.Opinion;
import com.youlai.boot.modules.opinion.model.form.OpinionForm;
import com.youlai.boot.modules.opinion.model.vo.OpinionVO;
import org.mapstruct.Mapper;

/**
 * 意见征集对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Mapper(componentModel = "spring")
public interface OpinionConverter {

    /**
     * 实体转换为视图对象
     *
     * @param entity 实体
     * @return 视图对象
     */
    OpinionVO entity2Vo(Opinion entity);

    /**
     * 实体分页转换为视图对象分页
     *
     * @param page 实体分页
     * @return 视图对象分页
     */
    Page<OpinionVO> entity2Page(Page<Opinion> page);

    /**
     * 实体转换为表单对象
     *
     * @param entity 实体
     * @return 表单对象
     */
    OpinionForm entity2Form(Opinion entity);

    /**
     * 表单对象转换为实体
     *
     * @param form 表单对象
     * @return 实体
     */
    Opinion form2Entity(OpinionForm form);
} 