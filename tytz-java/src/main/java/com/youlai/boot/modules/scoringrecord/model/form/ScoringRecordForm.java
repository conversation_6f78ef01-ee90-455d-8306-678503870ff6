package com.youlai.boot.modules.scoringrecord.model.form;

import com.youlai.boot.common.enums.ScoreTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 评分记录表单对象
 */
@Data
public class ScoringRecordForm {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商会会员名称
     */
    @NotBlank(message = "会员名称不能为空")
    private String memberName;

    /**
     * 所属部门
     */
    @NotBlank(message = "所属部门不能为空")
    private String department;

    /**
     * 评分类型
     */
    @NotBlank(message = "评分类型不能为空")
    private ScoreTypeEnum scoringType;

    /**
     * 评分详细内容
     */
    private String scoringDetail;

    /**
     * 得分
     */
    @NotNull(message = "得分不能为空")
    private Long score;
} 