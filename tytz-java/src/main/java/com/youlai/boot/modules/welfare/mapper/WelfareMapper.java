package com.youlai.boot.modules.welfare.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.welfare.model.entity.Welfare;
import com.youlai.boot.modules.welfare.model.query.WelfareQuery;
import com.youlai.boot.modules.welfare.model.vo.WelfareVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 公益信息Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Mapper
public interface WelfareMapper extends BaseMapper<Welfare> {

    /**
     * 获取公益信息分页数据
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return 公益信息分页数据
     */
    Page<WelfareVO> getWelfarePage(Page<WelfareVO> page, WelfareQuery queryParams);
} 