package com.youlai.boot.modules.businessmaterials.service.impl;

import cn.hutool.json.JSONUtil;
import com.youlai.boot.core.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.modules.businessmaterials.mapper.BusinessMaterialsMapper;
import com.youlai.boot.modules.businessmaterials.service.BusinessMaterialsService;
import com.youlai.boot.modules.businessmaterials.model.entity.BusinessMaterials;
import com.youlai.boot.modules.businessmaterials.model.form.BusinessMaterialsForm;
import com.youlai.boot.modules.businessmaterials.model.query.BusinessMaterialsQuery;
import com.youlai.boot.modules.businessmaterials.model.vo.BusinessMaterialsVO;
import com.youlai.boot.modules.businessmaterials.converter.BusinessMaterialsConverter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

/**
 * 营商材料服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Service
@RequiredArgsConstructor
public class BusinessMaterialsServiceImpl extends ServiceImpl<BusinessMaterialsMapper, BusinessMaterials> implements BusinessMaterialsService {

    private final BusinessMaterialsConverter businessMaterialsConverter;

    /**
    * 获取营商材料分页列表
    *
    * @param queryParams 查询参数
    * @return {@link IPage<BusinessMaterialsVO>} 营商材料分页列表
    */
    @Override
    public IPage<BusinessMaterialsVO> getBusinessMaterialsPage(BusinessMaterialsQuery queryParams) {
        Page<BusinessMaterialsVO> pageVO = this.baseMapper.getBusinessMaterialsPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );
        return pageVO;
    }
    
    /**
     * 获取营商材料表单数据
     *
     * @param id 营商材料ID
     * @return
     */
    @Override
    public BusinessMaterialsForm getBusinessMaterialsFormData(Long id) {
        BusinessMaterials entity = this.getById(id);
        List<BusinessMaterialsForm.FileDTO> list = JSONUtil.toList(entity.getAttachments(), BusinessMaterialsForm.FileDTO.class);
        BusinessMaterialsForm form = businessMaterialsConverter.toForm(entity);
        form.setAttachments(list);
        return form;
    }
    
    /**
     * 新增营商材料
     *
     * @param formData 营商材料表单对象
     * @return
     */
    @Override
    public boolean saveBusinessMaterials(BusinessMaterialsForm formData) {
        BusinessMaterials entity = businessMaterialsConverter.toEntity(formData);
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setCreateBy(SecurityUtils.getUserId());
        return this.save(entity);
    }
    
    /**
     * 更新营商材料
     *
     * @param id   营商材料ID
     * @param formData 营商材料表单对象
     * @return
     */
    @Override
    public boolean updateBusinessMaterials(Long id,BusinessMaterialsForm formData) {
        BusinessMaterials entity = businessMaterialsConverter.toEntity(formData);
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setUpdateBy(SecurityUtils.getUserId());
        return this.updateById(entity);
    }
    
    /**
     * 删除营商材料
     *
     * @param ids 营商材料ID，多个以英文逗号(,)分割
     * @return
     */
    @Override
    public boolean deleteBusinessMaterialss(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的营商材料数据为空");
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();
        return this.removeByIds(idList);
    }

}
