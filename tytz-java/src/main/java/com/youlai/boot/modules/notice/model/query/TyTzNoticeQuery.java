package com.youlai.boot.modules.notice.model.query;

import java.time.LocalDateTime;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.PublishStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 通知公告分页查询对象
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Schema(description ="通知公告查询对象")
@Getter
@Setter
public class TyTzNoticeQuery extends BasePageQuery {

    @Schema(description = "通知标题")
    private String keywords;

    @Schema(description = "发布状态")
    private PublishStatusEnum publishStatus;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

}
