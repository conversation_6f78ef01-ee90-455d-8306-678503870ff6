package ${package.Parent}.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
#if(${entityLombokModel})
import lombok.Getter;
import lombok.Setter;
    #if(${chainModel})
    import lombok.experimental.Accessors;
    #end
#end
import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * $!{table.comment} DTO
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${entityLombokModel})
@Getter
@Setter
    #if(${chainModel})
@Accessors(chain = true)
    #end
#end
@Schema( description = "$!{table.comment}传输层对象")
#if(${superEntityClass})
public class ${entity}DTO extends ${superEntityClass}#if(${activeRecord})<${entity}>#end {
#elseif(${activeRecord})
public class ${entity}DTO extends Model<${entity}> {
#elseif(${entitySerialVersionUID})
public class ${entity}DTO implements Serializable {
#else
public class ${entity}DTO {
#end
#if(${entitySerialVersionUID})

    private static final long serialVersionUID = 1L;
#end
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})

    #if(${field.keyFlag})
        #set($keyPropertyName=${field.propertyName})
    #end
    #if("$!field.comment" != "")
        @Schema(description = "${field.comment}")
    #end

private ${field.propertyType} ${field.propertyName};
#end
## ----------  END 字段循环遍历  ----------
#if(!${entityLombokModel})
    #foreach($field in ${table.fields})
        #if(${field.propertyType.equals("boolean")})
            #set($getprefix="is")
        #else
            #set($getprefix="get")
        #end

        public ${field.propertyType} ${getprefix}${field.capitalName}() {
        return ${field.propertyName};
    }

        #if(${chainModel})
            public ${entity} set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
        #else
            public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
        #end
        this.${field.propertyName} = ${field.propertyName};
        #if(${chainModel})
            return this;
        #end
    }
    #end
    ## --foreach end---
#end
## --end of #if(!${entityLombokModel})--
#if(${entityColumnConstant})
    #foreach($field in ${table.fields})

public static final String ${field.name.toUpperCase()} = "${field.name}";
    #end
#end
#if(${activeRecord})

    @Override
    public Serializable pkVal() {
    #if(${keyPropertyName})
        return this.${keyPropertyName};
    #else
        return null;
    #end
}
#end
#if(!${entityLombokModel})

    @Override
    public String toString() {
    return "${entity}{" +
        #foreach($field in ${table.fields})
            #if($!{foreach.index}==0)
                    "${field.propertyName} = " + ${field.propertyName} +
            #else
                    ", ${field.propertyName} = " + ${field.propertyName} +
            #end
        #end
            "}";
}
#end
}
