<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '天涯区智慧统战平台',
  },
}
</route>

<script lang="ts" setup>
import PageContainer from '@/components/PageContainer/index.vue'
import menu1 from '@/static/images/home/<USER>'
import menu2 from '@/static/images/home/<USER>'
import menu3 from '@/static/images/home/<USER>'
import menu4 from '@/static/images/home/<USER>'
import { getNoticeList } from '@/api/notice'
import type { TyTzNoticeVO } from '@/api/notice'
defineOptions({
  name: 'Home',
})

const noticeList = ref<TyTzNoticeVO[]>([])
getNoticeList({ page: 1, pageSize: 3 }).then((res) => {
  noticeList.value = res.list || []
})

const menus = [
  {
    title: '营商环境',
    color: '#F1814F',
    icon: menu1,
    background: 'linear-gradient(270deg, #F8D8CB 0%, #FDECE4 100%)',
    menuPath: '/pages/work/index',
    menuType: 'switchTab',
  },
  {
    title: '会员意见',
    color: '#F05552',
    icon: menu2,
    background: 'linear-gradient(270deg, #FACBC7 0%, #FBEAEB 100%)',
    menuPath: '/pages/work/index',
    menuType: 'switchTab',
  },
  {
    title: '新闻资讯',
    color: '#F1814F',
    icon: menu3,
    background: 'linear-gradient(270deg, #FFE0B1 0%, #F9F4DA 100%)',
    menuPath: '/pages/news/index',
    menuType: 'navigateTo',
  },
  {
    title: '约见申请',
    color: '#EE8A53',
    icon: menu4,
    background: 'linear-gradient(270deg, #F8D8CB 0%, #FDECE4 100%)',
    menuPath: '/pages/meet/apply',
    menuType: 'navigateTo',
  },
]

const handleMore = () => {
  useNavigate('/pages/notice/index')
}

function handleMenuClick(path: any) {
  useNavigate(path.menuPath, {}, path.menuType)
}

function handleToPage() {
  useNavigate('/pages/opinion/collect')
}
</script>

<template>
  <PageContainer theme="home">
    <!-- 要闻 -->
    <!-- <view class="flex color-white items-center justify-center">
      <image class="w-110rpx mr-6rpx" mode="widthFix" src="@/static/images/home/<USER>"></image>
      <view class="important-news">题党课 学好用好“千万工程”经验 有力有效推进乡村全面.</view>
    </view> -->

    <!-- 横幅 -->
    <image
      class="w-full mt-20rpx rounded-10rpx"
      mode="widthFix"
      src="@/static/images/home/<USER>"
    ></image>

    <!-- 通知 -->
    <view
      class="tongzhi flex items-center justify-center mt-20rpx p-20rpx border-1rpx border-#fff border-solid rounded-10rpx"
    >
      <image class="w-90rpx" mode="widthFix" src="@/static/images/home/<USER>"></image>
      <view class="w-1px h-72rpx bg-#E5E5E5 mx-20rpx"></view>
      <view class="flex-1 h-full text-24rpx mr-24rpx overflow-hidden">
        <view class="tongzhi-text" v-for="notice in noticeList" :key="notice.id">
          {{ notice.title }}
        </view>
      </view>
      <!-- TODO -->
      <view class="color-#A6A6A6 text-22rpx self-start" @click="handleMore">更多+</view>
    </view>

    <view class="text-30rpx font-700 color-#383838 flex items-center my-20rpx">
      <image
        class="w-37rpx mr-10rpx"
        mode="widthFix"
        src="@/static/images/home/<USER>"
      ></image>
      工作中心
    </view>
    <!-- 工作中心 -->
    <view class="flex flex-wrap justify-between">
      <view
        class="w-345rpx h-140rpx mt-20rpx menu-item rounded-10rpx pt-36rpx pl-32rpx relative"
        :style="{
          background: menu.background,
        }"
        v-for="menu in menus"
        :key="menu.title"
        @click="handleMenuClick(menu)"
      >
        <view class="text-30rpx font-700" :style="{ color: menu.color }">{{ menu.title }}</view>
        <image class="w-79rpx h-79rpx absolute bottom-10rpx right-10rpx" :src="menu.icon"></image>
      </view>
    </view>

    <view class="text-30rpx font-700 color-#383838 flex items-center my-20rpx">
      <image
        class="w-37rpx mr-10rpx"
        mode="widthFix"
        src="@/static/images/home/<USER>"
      ></image>
      商会会员意见征集
    </view>
    <view class="suggestion px-30rpx py-35rpx rounded-10rpx">
      <view class="color-#383838 text-30rpx">关于营商环境改进有什么建议</view>
      <view class="color-#666666 text-28rpx mt-10rpx">
        为进一步做好三亚市天涯区优化营商环境工作，着力解决企业在生产经营活动中面临的突出问题。提升经主题感受度和满意度，现广泛征集各类企业对园区优化营商环境的意见建议..
      </view>
      <view
        class="suggestion-btn w-184rpx h-76rpx color-white text-28rpx rounded-10rpx flex items-center justify-center mx-auto mt-20rpx mt-60rpx"
        @click="handleToPage()"
      >
        赶快提交
      </view>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped>
.important-news {
  overflow: hidden;
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tongzhi {
  background: linear-gradient(180deg, rgba(253, 245, 241, 1) 0%, rgba(255, 255, 255, 1) 100%);
  .tongzhi-text {
    overflow: hidden;
    color: #383838;
    text-overflow: ellipsis;
    white-space: nowrap;
    + .tongzhi-text {
      margin-top: 4rpx;
    }
  }
}
.menu-item {
  &:nth-child(1),
  &:nth-child(2) {
    margin-top: 0;
  }
}
.suggestion {
  background: linear-gradient(180deg, rgba(253, 245, 241, 1) 0%, rgba(255, 255, 255, 1) 100%);
  .suggestion-btn {
    background: linear-gradient(90deg, #dc4f3f 0%, #ef4142 100%);
  }
}
</style>
