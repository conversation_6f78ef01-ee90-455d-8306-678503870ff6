import type { FormItemProps, FormItemRule, FormProps as ElFormProps } from "element-plus";

import type { ComponentTypeMap } from "@/components/CustomComponent/type";
export interface FormProps extends Partial<ElFormProps> {
  items?: FormItemsUnionTypeList;
  /**
   * form-item 占据的宽度
   * 会被 items[width] 覆盖
   */
  itemWidth?: string | number;
  placeholder?: string;
}

/** form items 联合类型组成的数组 */
export type FormItemsUnionTypeList = FormItemUnionType[];

/** form items 联合类型 */
export type FormItemUnionType = {
  [K in keyof ComponentTypeMap]: {
    is: K;
    props?: ComponentTypeMap[K] | any;
    /** FormItem组件 props */
    formItem: Partial<FormItemProps> & {
      prop: string;
      rules?: FormItemRule[];
    };
    /** 默认值 */
    initialValue?: any;
    /** 提交时格式化数据 在调用 form.validate 方法通过后调用 */
    formatter?: (v?: any) => any;
    /** form-item 占据的宽度 */
    width?: string | number;
    /** 日期范围选择器 的 开始结束字段 */
    dateRangeFields?: [string, string];
  };
}[keyof ComponentTypeMap];
