import request from "@/utils/request";

const API_PREFIX = "/api/v1/opinions";

/**
 * 问题分页查询对象
 */
export interface opinionsPageQuery extends PageQuery {
  /** 商会会员名称 */
  memberName?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
/**意见征集分页列表 */
export interface opinionsPageVO {
  /** 主键 */
  id?: number;

  /** 意见内容 */
  content?: string;

  /** 商会会员名称 */
  memberName?: string;

  /** 所属单位 */
  department?: string;

  /** 创建时间 */
  createTime?: string;

  /** 联系方式 */
  contact?: string;
}
/**意见征集分页列表 */
export const opinionsPageApi = (queryParams: opinionsPageQuery) => {
  return request<any, PageResult<opinionsPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 获取意见征集表单数据*/
export const opinionsViewApi = (id: string) => {
  return request<any, opinionsPageVO>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/**导出 */
export const opinionsExportApi = (queryParams: any) => {
  return request<any, Blob>({
    url: `${API_PREFIX}/export`,
    method: "POST",
    data: queryParams,
    responseType: "blob",
  });
};
