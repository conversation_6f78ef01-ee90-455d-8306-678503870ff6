<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.welfare.mapper.WelfareMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youlai.boot.modules.welfare.model.entity.Welfare">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="department" property="department"/>
        <result column="participants" property="participants"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="content" property="content"/>
        <result column="attachments" property="attachments"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 公益信息分页结果映射 -->
    <resultMap id="WelfareVOResultMap" type="com.youlai.boot.modules.welfare.model.vo.WelfareVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="department" property="department"/>
        <result column="participants" property="participants"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="content" property="content"/>
        <result column="attachments" property="attachments"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 获取公益信息分页列表 -->
    <select id="getWelfarePage" resultMap="WelfareVOResultMap">
        SELECT
            id,
            title,
            department,
            participants,
            start_time,
            end_time,
            content,
            attachments,
            create_time
        FROM
            tsz_welfare
        <where>
            is_deleted = 0
            <if test="queryParams.title != null and queryParams.title != ''">
                AND title LIKE CONCAT('%', #{queryParams.title}, '%')
            </if>
            <if test="queryParams.department != null and queryParams.department != ''">
                AND department LIKE CONCAT('%', #{queryParams.department}, '%')
            </if>
            <if test="queryParams.startTimeBegin != null">
                AND start_time &gt;= #{queryParams.startTimeBegin}
            </if>
            <if test="queryParams.startTimeEnd != null">
                AND start_time &lt;= #{queryParams.startTimeEnd}
            </if>
        </where>
        ORDER BY
            create_time DESC
    </select>
</mapper> 