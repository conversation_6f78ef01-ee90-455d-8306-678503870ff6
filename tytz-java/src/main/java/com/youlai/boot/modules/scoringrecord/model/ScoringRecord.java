package com.youlai.boot.modules.scoringrecord.model;

import com.baomidou.mybatisplus.annotation.*;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评分记录实体类
 */
@Data
@TableName("tsz_scoring_record")
public class ScoringRecord extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商会会员名称
     */
    private String memberName;


    /**
     * 商会会员id
     */
    private Long memberId;

    /**
     * 会议ID
     */
    private Long meetingId;

    /**
     * 工作ID
     */
    private Long workId;

    /**
     * 活动ID
     */
    private Long activityId;


    /**
     * 问题ID
     */
    private Long problemId;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 评分类型
     */
    private ScoreTypeEnum scoringType;

    /**
     * 评分详细内容
     */
    private String scoringDetail;

    /**
     * 得分
     */
    private Long score;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;


    /**
     * 是否删除（0：未删除，1：已删除）
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 年度
     */
    private String year;
}