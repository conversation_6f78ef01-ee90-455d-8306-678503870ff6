package com.youlai.boot.modules.businessmaterials.model.vo;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * 营商材料视图对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@Schema( description = "营商材料视图对象")
public class BusinessMaterialsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "材料名称")
    private String title;
    @Schema(description = "材料内容")
    private String content;
    @Schema(description = "附件JSON数据，包含文件名、路径等信息")
    private String attachments;
    @Schema(description = "提交人ID")
    private Long submitBy;
    @Schema(description = "活动开始时间")
    private LocalDateTime activeStartTime;
    @Schema(description = "活动结束时间")
    private LocalDateTime activeEndTime;
}
