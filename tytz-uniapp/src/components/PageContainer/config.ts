import type { CSSProperties } from 'vue'
import type { PageContainerThemes } from './type'

const staticUrl = import.meta.env.VITE_STATIC_URL

const publicImags = {
  defaultBg: `${staticUrl}/components/default-bg.png`,
  pageBg: `${staticUrl}/components/page-bg.png`,
  loginBg: `${staticUrl}/components/login-bg.png`,
}

/**
 * 主题配置
 */
export const pageContainerTheme: Record<
  PageContainerThemes,
  {
    /** 导航栏样式 */
    navigation: CSSProperties
    /** 导航栏文字颜色 */
    navigationBarTextColor: string
    /** 背景视图样式 */
    backgroundView?: CSSProperties
    /** 容器样式 */
    container: CSSProperties
    /** 插槽外部盒子样式 */
    slot?: CSSProperties
  }
> = {
  /**
   * 默认主题
   */
  default: {
    container: {},
    navigation: {
      backgroundImage: `url(${publicImags.defaultBg})`,
      backgroundPosition: 'top',
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
    },
    navigationBarTextColor: '#ffffff',
  },
  /**
   * 首页主题
   */
  home: {
    container: {
      backgroundColor: '#F5F6FA',
    },
    navigation: {},
    navigationBarTextColor: '#ffffff',
    backgroundView: {
      backgroundImage: `url(${publicImags.defaultBg})`,
      backgroundPosition: 'top',
      backgroundSize: '100%, 100%',
      backgroundRepeat: 'no-repeat',
    },
  },
  /**
   * 个人中心主题
   */
  person: {
    container: {
      backgroundColor: '#C9141A',
    },
    navigation: {},
    navigationBarTextColor: '#ffffff',
    backgroundView: {
      backgroundImage: `url(${publicImags.pageBg})`,
      backgroundPosition: 'top',
      backgroundSize: '100%, 100%',
      backgroundRepeat: 'no-repeat',
    },
    slot: {},
  },

  /**
   * 登录主题
   */
  login: {
    container: {
      backgroundColor: '#F5F6FA',
    },
    navigation: {},
    navigationBarTextColor: '#ffffff',
    backgroundView: {
      backgroundImage: `url(${publicImags.loginBg})`,
      backgroundPosition: 'center',
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
    },
  },
  /**
   * 页面主题 带主体色背景
   */
  page: {
    container: {
      backgroundColor: '#C9141A',
    },
    navigation: {},
    navigationBarTextColor: '#ffffff',
    backgroundView: {
      backgroundImage: `url(${publicImags.pageBg})`,
      backgroundPosition: 'top',
      backgroundSize: '100%, 100%',
      backgroundRepeat: 'no-repeat',
    },
    slot: {
      borderRadius: '4px',
      overflow: 'hidden',
      backgroundColor: '#ffffff',
    },
  },
  /**
   * 页面主题 带浅灰色色背景
   */
  contrastPage: {
    container: {
      backgroundColor: '#F7F7F7',
    },
    navigation: {
      backgroundImage: `url(${publicImags.defaultBg})`,
      backgroundPosition: 'top',
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
    },
    navigationBarTextColor: '#ffffff',
  },
}
