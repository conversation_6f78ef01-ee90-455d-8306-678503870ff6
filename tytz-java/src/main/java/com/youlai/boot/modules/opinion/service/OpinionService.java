package com.youlai.boot.modules.opinion.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.modules.opinion.model.entity.Opinion;
import com.youlai.boot.modules.opinion.model.form.OpinionForm;
import com.youlai.boot.modules.opinion.model.query.OpinionQuery;
import com.youlai.boot.modules.opinion.model.query.MyOpinionQuery;
import com.youlai.boot.modules.opinion.model.vo.OpinionVO;

import java.util.List;

/**
 * 意见征集服务接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface OpinionService extends IService<Opinion> {

    /**
     * 获取意见征集分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    IPage<OpinionVO> getOpinionPage(OpinionQuery queryParams);

    /**
     * 获取我的提交的意见征集分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    IPage<OpinionVO> getMyOpinionPage(MyOpinionQuery queryParams);

    /**
     * 获取意见征集表单数据
     *
     * @param id 意见征集ID
     * @return 表单数据
     */
    OpinionForm getOpinionFormData(Long id);

    /**
     * 新增意见征集
     *
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean saveOpinion(OpinionForm formData);

    /**
     * 修改意见征集
     *
     * @param id 意见征集ID
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean updateOpinion(Long id, OpinionForm formData);

    /**
     * 删除意见征集
     *
     * @param ids 意见征集ID（逗号分隔）
     * @return 是否成功
     */
    boolean deleteOpinions(String ids);

    /**
     * 获取导出数据列表
     *
     * @param queryParams 查询参数
     * @return 导出数据列表
     */
    List<OpinionVO> getExportList(OpinionQuery queryParams);
} 