package com.youlai.boot.modules.activity.service;

import com.youlai.boot.modules.activity.model.entity.Activity;
import com.youlai.boot.modules.activity.model.form.ActivityForm;
import com.youlai.boot.modules.activity.model.query.ActivityQuery;
import com.youlai.boot.modules.activity.model.vo.ActivityVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import com.youlai.boot.system.model.bo.UserBO;

/**
 * 活动管理服务类
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
public interface ActivityService extends IService<Activity> {

    /**
     * 活动管理分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<ActivityVO> getActivityPage(ActivityQuery queryParams);

    /**
     * 获取活动管理表单数据
     *
     * @param id 活动ID
     * @return 表单数据
     */
    ActivityForm getActivityFormData(Long id);

    /**
     * 新增活动
     *
     * @param formData 活动表单对象
     * @return 是否成功
     */
    boolean saveActivity(ActivityForm formData);

    /**
     * 修改活动
     *
     * @param id 活动ID
     * @param formData 活动表单对象
     * @return 是否成功
     */
    boolean updateActivity(Long id, ActivityForm formData);

    /**
     * 删除活动
     *
     * @param ids 活动ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteActivities(String ids);

    /**
     * 根据用户ID列表获取UserBO列表
     *
     * @param userIds 用户ID列表
     * @return UserBO列表
     */
    List<UserBO> getUserBOListByIds(List<Long> userIds);
}