package com.youlai.boot.modules.opinion.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 意见征集表单对象
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Schema(description = "意见征集表单对象")
@Data
public class OpinionForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;
    
    @Schema(description = "意见内容")
    @NotBlank(message = "意见内容不能为空")
    private String content;
    
    @Schema(description = "商会会员名称")
    private String memberName;

    @Schema(description = "所属单位")
    private String department;

    @Schema(description = "联系方式")
    private String contact;

    @Schema(description = "创建时间")
    private LocalDate createTime;

    @Schema(description = "商会会员id")
    private Long MemberId;

    @Schema(description = "用户身份")
    private String memberRole;
} 