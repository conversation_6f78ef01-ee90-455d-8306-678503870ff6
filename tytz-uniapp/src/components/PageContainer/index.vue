<script setup lang="ts">
import { getPlatformPageSize } from '@/utils/style'
import { pageContainerTheme } from './config'
import { isTabbarPage, getCurrentPageConfig } from '@/utils/router'
import type { PageContainerThemes } from './type'
export interface PageContainerProps {
  /**
   * 标题
   */
  title?: string
  /**
   * 是否显示导航栏
   */
  navigation?: boolean
  /**
   * 主题
   */
  theme?: PageContainerThemes
  /**
   * 插槽显示方式
   */
  slotType?: 'scroll' | 'view' | 'custom'
}
const pageConfig = getCurrentPageConfig()
const props = withDefaults(defineProps<PageContainerProps>(), {
  theme: 'default',
  backgroundColor: '#fff',
  navigation: true,
  slotType: 'scroll',
})

const { width, height } = getPlatformPageSize()

const title = computed(() => {
  return props.title || pageConfig.style.navigationBarTitleText
})

const theme = computed(() => {
  return pageContainerTheme[props.theme]
})

const isLeftArrow = !isTabbarPage()

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="page-container relative flex flex-col" :style="theme.container">
    <!-- 专门显示背景图片, 避免被背景颜色覆盖 -->
    <view class="absolute top-0 left-0 w-full h-full" :style="theme.backgroundView"></view>
    <view class="page-container-navbar" :style="theme.navigation" v-if="navigation">
      <wd-navbar
        :title="title"
        safe-area-inset-top
        :bordered="false"
        custom-style="background-color: transparent;"
        @click-left="handleClickLeft"
      >
        <template #left v-if="isLeftArrow">
          <wd-icon
            name="thin-arrow-left"
            size="18px"
            :color="theme.navigationBarTextColor"
          ></wd-icon>
        </template>
        <template #title>
          <view :style="{ color: theme.navigationBarTextColor }">{{ title }}</view>
        </template>
      </wd-navbar>
    </view>
    <scroll-view class="flex-1 my-20rpx relative" scroll-y v-if="slotType === 'scroll'">
      <view class="flex min-h-full flex-col mx-10px" :style="theme.slot">
        <slot></slot>
      </view>
    </scroll-view>

    <view
      class="flex-1 flex flex-col m-10px overflow-hidden relative"
      :style="theme.slot"
      v-else-if="slotType === 'view'"
    >
      <slot></slot>
    </view>

    <view
      v-else-if="slotType === 'custom'"
      class="flex-1 flex flex-col overflow-hidden relative"
      :style="theme.slot"
    >
      <slot></slot>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  width: v-bind(width);
  height: v-bind(height);
}
</style>
