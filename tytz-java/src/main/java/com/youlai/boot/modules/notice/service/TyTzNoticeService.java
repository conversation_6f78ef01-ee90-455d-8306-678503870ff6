package com.youlai.boot.modules.notice.service;

import com.youlai.boot.modules.notice.model.entity.TyTzNotice;
import com.youlai.boot.modules.notice.model.form.TyTzNoticeForm;
import com.youlai.boot.modules.notice.model.query.TyTzNoticeQuery;
import com.youlai.boot.modules.notice.model.vo.TyTzNoticeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 通知公告服务类
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
public interface TyTzNoticeService extends IService<TyTzNotice> {

    /**
     *通知公告分页列表
     *
     * @return
     */
    IPage<TyTzNoticeVO> getNoticePage(TyTzNoticeQuery queryParams);

    /**
     * 获取通知公告表单数据
     *
     * @param id 通知公告ID
     * @return
     */
     TyTzNoticeForm getNoticeFormData(Long id);

    /**
     * 新增通知公告
     *
     * @param formData 通知公告表单对象
     * @return
     */
    boolean saveNotice(TyTzNoticeForm formData);

    /**
     * 修改通知公告
     *
     * @param id   通知公告ID
     * @param formData 通知公告表单对象
     * @return
     */
    boolean updateNotice(Long id, TyTzNoticeForm formData);

    /**
     * 删除通知公告
     *
     * @param ids 通知公告ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteNotices(String ids);

}
