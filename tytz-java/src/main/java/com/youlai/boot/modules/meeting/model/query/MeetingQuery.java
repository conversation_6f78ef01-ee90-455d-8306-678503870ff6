package com.youlai.boot.modules.meeting.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.MeetingTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * 会议管理分页查询对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Schema(description ="会议管理查询对象")
@Getter
@Setter
public class MeetingQuery extends BasePageQuery {
    
    @Schema(description = "会议名称")
    private String title;

    @Schema(description = "会议类型")
    private MeetingTypeEnum meetingType;
    
    @Schema(description = "组织者")
    private String department;
    
    @Schema(description = "开始时间")
    private String startTime;
    
    @Schema(description = "结束时间")
    private String endTime;
} 