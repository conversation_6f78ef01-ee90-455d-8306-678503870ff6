import request from "@/utils/request";
// 菜单基础URL
const MENU_BASE_URL = "/api/v1/menus";

const MenuAPI = {
  /**
   * 获取当前用户的路由列表
   * <p/>
   * 无需传入角色，后端解析token获取角色自行判断是否拥有路由的权限
   *
   * @returns 路由列表
   */
  getRoutes() {
    return request<any, RouteVO[]>({
      url: `${MENU_BASE_URL}/routes`,
      method: "get",
    });
    /* return [
      {
        path: "/biz_env",
        component: "Layout",
        redirect: "/issue_mgmt/index",
        name: "/biz_env",
        meta: {
          title: "营商环境管理",
          icon: "menu",
          hidden: false,
          alwaysShow: false,
          params: null,
        },
        children: [
          {
            path: "issue",
            component: "issue_mgmt/index",
            name: "Issue",
            meta: {
              title: "问题审核",
              icon: "code",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "approval",
            component: "approval_mgmt/index",
            name: "Approval",
            meta: {
              title: "领导批示",
              icon: "role",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
        ],
      },
      {
        path: "/feedback",
        component: "Layout",
        name: "/feedback",
        meta: {
          title: "意见采集管理",
          icon: "menu",
          hidden: false,
          alwaysShow: false,
          params: null,
        },
        icon: "menu",
        children: [
          {
            path: "feedback",
            component: "feedback_mgmt/index",
            name: "Feedback",
            meta: {
              title: "意见征集汇总",
              icon: "code",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
            children: [],
          },
        ],
      },
      {
        path: "/appoint",
        component: "Layout",
        redirect: "/appointment_mgmt/index",
        name: "/appoint",
        meta: {
          title: "约见信息管理",
          icon: "menu",
          hidden: false,
          alwaysShow: false,
          params: null,
        },
        children: [
          {
            path: "appoint",
            component: "appointment_mgmt/index",
            name: "Appoint",
            meta: {
              title: "约见办理",
              icon: "code",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "appo_feedback",
            component: "appointment_mgmt/feedback",
            name: "appoFeedback",
            meta: {
              title: "约见信息反馈",
              icon: "role",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
        ],
      },
      {
        path: "/news",
        component: "Layout",
        redirect: "/news_mgmt/news",
        name: "/news",
        meta: {
          title: "新闻资讯管理",
          icon: "menu",
          hidden: false,
          alwaysShow: false,
          params: null,
        },
        children: [
          {
            path: "news",
            component: "news_mgmt/news",
            name: "News",
            meta: {
              title: "新闻资讯",
              icon: "code",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "notice",
            component: "news_mgmt/notice",
            name: "Notice",
            meta: {
              title: "通知公告",
              icon: "role",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
        ],
      },
      {
        path: "/rating",
        component: "Layout",
        redirect: "/rating_mgmt/activity",
        name: "/activity",
        meta: {
          title: "评分信息管理",
          icon: "menu",
          hidden: false,
          alwaysShow: false,
          params: null,
        },
        children: [
          {
            path: "activity",
            component: "rating_mgmt/activity",
            name: "Activity",
            meta: {
              title: "活动信息",
              icon: "code",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "meeting",
            component: "rating_mgmt/meeting",
            name: "Meeting",
            meta: {
              title: "会议信息",
              icon: "role",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "annual_key_tasks",
            component: "rating_mgmt/annual_key_tasks",
            name: "AnnualKeyTasks",
            meta: {
              title: "年度重点工作",
              icon: "role",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
        ],
      },
      {
        path: "/member_rating",
        component: "Layout",
        redirect: "/member_rating_mgmt/activity",
        name: "/member_rating",
        meta: {
          title: "委员履职评分",
          icon: "menu",
          hidden: false,
          alwaysShow: false,
          params: null,
        },
        children: [
          {
            path: "scoringRules",
            component: "member_rating_mgmt/scoringRules",
            name: "ScoringRules",
            meta: {
              title: "评分规则",
              icon: "code",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "evaluation",
            component: "member_rating_mgmt/evaluation",
            name: "Evaluation",
            meta: {
              title: "评价表管理",
              icon: "role",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
        ],
      },
      {
        path: "/system",
        component: "Layout",
        redirect: "/system/user",
        name: "/system",
        meta: {
          title: "系统管理",
          icon: "system",
          hidden: false,
          alwaysShow: false,
          params: null,
        },
        children: [
          {
            path: "user",
            component: "system/user/index",
            name: "User",
            meta: {
              title: "用户管理",
              icon: "el-icon-User",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "role",
            component: "system/role/index",
            name: "Role",
            meta: {
              title: "角色管理",
              icon: "role",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "dept",
            component: "system/dept/index",
            name: "Dept",
            meta: {
              title: "部门管理",
              icon: "tree",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "menu",
            component: "system/menu/index",
            name: "SysMenu",
            meta: {
              title: "菜单管理",
              icon: "menu",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
          {
            path: "config",
            component: "system/config/index",
            name: "Config",
            meta: {
              title: "系统配置",
              icon: "setting",
              hidden: false,
              keepAlive: true,
              alwaysShow: false,
              params: null,
            },
          },
        ],
      },
    ]; */
  },
  /**
   * 获取菜单树形列表
   * @param queryParams 查询参数
   * @returns 菜单树形列表
   */
  getList(queryParams: MenuQuery) {
    return request<any, MenuVO[]>({
      url: `${MENU_BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取菜单下拉数据源
   *
   * @returns 菜单下拉数据源
   */
  getOptions(onlyParent?: boolean) {
    return request<any, OptionType[]>({
      url: `${MENU_BASE_URL}/options`,
      method: "get",
      params: { onlyParent: onlyParent },
    });
  },

  /**
   * 获取菜单表单数据
   *
   * @param id 菜单ID
   */
  getFormData(id: string) {
    return request<any, MenuForm>({
      url: `${MENU_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /**
   * 添加菜单
   *
   * @param data 菜单表单数据
   * @returns 请求结果
   */
  add(data: MenuForm) {
    return request({
      url: `${MENU_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 修改菜单
   *
   * @param id 菜单ID
   * @param data 菜单表单数据
   * @returns 请求结果
   */
  update(id: string, data: MenuForm) {
    return request({
      url: `${MENU_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 删除菜单
   *
   * @param id 菜单ID
   * @returns 请求结果
   */
  deleteById(id: number) {
    return request({
      url: `${MENU_BASE_URL}/${id}`,
      method: "delete",
    });
  },
};

export default MenuAPI;

import type { MenuTypeEnum } from "@/enums/MenuTypeEnum";

/** 菜单查询参数 */
export interface MenuQuery {
  /** 搜索关键字 */
  keywords?: string;
}

/** 菜单视图对象 */
export interface MenuVO {
  /** 子菜单 */
  children?: MenuVO[];
  /** 组件路径 */
  component?: string;
  /** ICON */
  icon?: string;
  /** 菜单ID */
  id?: string;
  /** 菜单名称 */
  name?: string;
  /** 父菜单ID */
  parentId?: string;
  /** 按钮权限标识 */
  perm?: string;
  /** 跳转路径 */
  redirect?: string;
  /** 路由名称 */
  routeName?: string;
  /** 路由相对路径 */
  routePath?: string;
  /** 菜单排序(数字越小排名越靠前) */
  sort?: number;
  /** 菜单 */
  type?: MenuTypeEnum;
  /** 菜单是否可见(1:显示;0:隐藏) */
  visible?: number;
}

/** 菜单表单对象 */
export interface MenuForm {
  /** 菜单ID */
  id?: string;
  /** 父菜单ID */
  parentId?: string;
  /** 菜单名称 */
  name?: string;
  /** 菜单是否可见(1-是 0-否) */
  visible: number;
  /** ICON */
  icon?: string;
  /** 排序 */
  sort?: number;
  /** 路由名称 */
  routeName?: string;
  /** 路由路径 */
  routePath?: string;
  /** 组件路径 */
  component?: string;
  /** 跳转路由路径 */
  redirect?: string;
  /** 菜单 */
  type?: MenuTypeEnum;
  /** 权限标识 */
  perm?: string;
  /** 【菜单】是否开启页面缓存 */
  keepAlive?: number;
  /** 【目录】只有一个子路由是否始终显示 */
  alwaysShow?: number;
  /** 参数 */
  params?: KeyValue[];
}

interface KeyValue {
  key: string;
  value: string;
}

/** RouteVO，路由对象 */
export interface RouteVO {
  /** 子路由列表 */
  children?: RouteVO[];
  /** 组件路径 */
  component?: string;
  /** 路由属性 */
  meta?: Meta;
  /** 路由名称 */
  name?: string;
  /** 路由路径 */
  path?: string;
  /** 跳转链接 */
  redirect?: string;
}

/** Meta，路由属性 */
export interface Meta {
  /** 【目录】只有一个子路由是否始终显示 */
  alwaysShow?: boolean;
  /** 是否隐藏(true-是 false-否) */
  hidden?: boolean;
  /** ICON */
  icon?: string;
  /** 【菜单】是否开启页面缓存 */
  keepAlive?: boolean;
  /** 路由title */
  title?: string;
}
