package com.youlai.boot.modules.meeting.controller;

import com.youlai.boot.modules.meeting.service.MeetingService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.youlai.boot.modules.meeting.model.form.MeetingForm;
import com.youlai.boot.modules.meeting.model.query.MeetingQuery;
import com.youlai.boot.modules.meeting.model.vo.MeetingVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * 会议管理前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Tag(name = "会议管理接口")
@RestController
@RequestMapping("/api/v1/meetings")
@RequiredArgsConstructor
public class MeetingController {

    private final MeetingService meetingService;

    @Operation(summary = "会议分页列表")
    @GetMapping("/page")
    // @PreAuthorize("@ss.hasPerm('modules.meeting:meeting:query')")
    public PageResult<MeetingVO> getMeetingPage(MeetingQuery queryParams) {
        IPage<MeetingVO> result = meetingService.getMeetingPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增会议")
    @PostMapping
    // @PreAuthorize("@ss.hasPerm('modules.meeting:meeting:add')")
    public Result<Void> saveMeeting(@RequestBody @Valid MeetingForm formData) {
        boolean result = meetingService.saveMeeting(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取会议表单数据")
    @GetMapping("/{id}/form")
    // @PreAuthorize("@ss.hasPerm('modules.meeting:meeting:edit')")
    public Result<MeetingForm> getMeetingForm(
        @Parameter(description = "会议ID") @PathVariable Long id
    ) {
        MeetingForm formData = meetingService.getMeetingFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改会议")
    @PutMapping(value = "/{id}")
    // @PreAuthorize("@ss.hasPerm('modules.meeting:meeting:edit')")
    public Result<Void> updateMeeting(
            @Parameter(description = "会议ID") @PathVariable Long id,
            @RequestBody @Validated MeetingForm formData
    ) {
        boolean result = meetingService.updateMeeting(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除会议")
    @DeleteMapping("/{ids}")
    // @PreAuthorize("@ss.hasPerm('modules.meeting:meeting:delete')")
    public Result<Void> deleteMeetings(
        @Parameter(description = "会议ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = meetingService.deleteMeetings(ids);
        return Result.judge(result);
    }
} 