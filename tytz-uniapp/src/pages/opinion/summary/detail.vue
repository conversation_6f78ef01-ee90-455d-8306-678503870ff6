<route lang="json5">
{
  style: {
    navigationBarTitleText: '意见详情',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { getOpinionDetail } from '@/api/opinion/summary'
import dayjs from 'dayjs'

const statusEnum = {
  WAIT: {
    label: '待处理',
    color: '#808080',
  },
  PASS: {
    label: '已采纳',
    color: '#8BC349',
  },
  REJECT: {
    label: '不予采纳',
    color: '#EF4142',
  },
}

const props = defineProps<{
  id: string
}>()

const detail = ref<Awaited<ReturnType<typeof getOpinionDetail>>>()

onMounted(async () => {
  const res = await getOpinionDetail(props.id)
  detail.value = res
})
</script>

<template>
  <PageContainer theme="page">
    <view class="p-4 bg-white">
      <view class="mb-3 items-start">
        <text class="text-sm text-gray-500 w-30 flex-shrink-0">意见描述</text>
        <view class="text-sm text-gray-800 leading-relaxed mt-2">{{ detail?.content }}</view>
      </view>

      <view class="mt-6 p-3 border-t border-gray-200 bg-#F8F8F9">
        <view class="flex items-center">
          <text class="text-sm text-gray-500 w-30 flex-shrink-0">提交时间:</text>
          <text class="text-sm text-gray-800">
            {{ detail?.createTime }}
          </text>
        </view>
      </view>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped></style>
