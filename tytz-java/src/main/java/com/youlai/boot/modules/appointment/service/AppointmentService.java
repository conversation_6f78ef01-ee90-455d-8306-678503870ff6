package com.youlai.boot.modules.appointment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.common.enums.AppoinitMentEnum;
import com.youlai.boot.modules.appointment.model.dto.AppointmentDTO;
import com.youlai.boot.modules.appointment.model.entity.Appointment;
import com.youlai.boot.modules.appointment.model.form.AppointmentForm;
import com.youlai.boot.modules.appointment.model.query.AppointmentQuery;
import com.youlai.boot.modules.appointment.model.query.MyAppointmentQuery;
import com.youlai.boot.modules.appointment.model.vo.AppointmentVO;

import java.util.List;
import java.util.Map;

/**
 * 约见管理服务接口
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface AppointmentService extends IService<Appointment> {

    /**
     * 获取约见管理分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    IPage<AppointmentVO> getAppointmentPage(AppointmentQuery queryParams);

    /**
     * 获取约见管理表单数据
     *
     * @param id 约见管理ID
     * @return 表单数据
     */
    AppointmentForm getAppointmentFormData(Long id);

    /**
     * 新增约见管理
     *
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean saveAppointment(AppointmentForm formData);

    /**
     * 获取我的提交的约见申请分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    IPage<AppointmentVO> getMyAppointmentPage(MyAppointmentQuery queryParams);

    /**
     * 修改约见管理
     *
     * @param id 约见管理ID
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean updateAppointment(Long id, AppointmentForm formData);

    /**
     * 删除约见管理
     *
     * @param ids 约见管理ID（逗号分隔）
     * @return 是否成功
     */
    boolean deleteAppointments(String ids);
    
    /**
     * 处理约见申请
     *
     * @param id 约见管理ID
     * @param dto 表单数据
     * @return 是否成功
     */
    boolean handleAppointment(Long id, AppointmentDTO dto);
    
    /**
     * 提交约见反馈
     *
     * @param id 约见管理ID
     * @param feedback 反馈内容
     * @return 是否成功
     */
    boolean submitFeedback(Long id, String feedback);

    /**
     * 获取约见管理导出数据列表
     *
     * @param queryParams 查询参数
     * @return 数据列表
     */
    List<AppointmentVO> getExportList(AppointmentQuery queryParams);


    /**
     * 获取各约见状态的列表
     *
     * @param status 约见状态
     * @return 约见列表
     */
    List<AppointmentVO> getAppointmentListByStatus(AppoinitMentEnum status);
} 