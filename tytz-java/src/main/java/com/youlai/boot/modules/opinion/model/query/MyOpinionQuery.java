package com.youlai.boot.modules.opinion.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 意见征集查询对象
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "意见征集查询对象")
@Data
public class MyOpinionQuery extends BasePageQuery {

    @Schema(description = "内容关键字")
    private String keyword;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;
} 