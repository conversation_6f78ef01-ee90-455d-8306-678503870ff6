package com.youlai.boot.modules.meeting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youlai.boot.modules.meeting.model.entity.Meeting;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.meeting.model.query.MeetingQuery;
import com.youlai.boot.modules.meeting.model.vo.MeetingVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 会议管理Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper
public interface MeetingMapper extends BaseMapper<Meeting> {

    /**
     * 获取会议管理分页数据
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return 会议分页数据
     */
    Page<MeetingVO> getMeetingPage(Page<MeetingVO> page, MeetingQuery queryParams);
} 