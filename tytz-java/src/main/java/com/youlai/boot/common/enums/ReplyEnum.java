package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 数据权限枚举
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Getter
public enum ReplyEnum implements IBaseEnum<Integer> {


    REPLIED(0, "已答复"),
    UN_REPLIED(1, "未答复");

    private final Integer value;

    private final String label;

    ReplyEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
}
