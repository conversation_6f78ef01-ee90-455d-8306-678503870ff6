name: 🚨部署到正式环境
on:
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Java for package
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'oracle'

      - name: Package the application
        run: mvn clean package -DskipTests

      - name: Rename the JAR file
        run: mv target/youlai-boot.jar app.jar

      - name: Deploy to server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          source: "app.jar"
          target: "/opt/tytz_prod/admin-api"

      - name: Restart service
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          script: cd /opt/tytz_prod && docker compose down tytz-prod-admin-api && docker compose up -d tytz-prod-admin-api
