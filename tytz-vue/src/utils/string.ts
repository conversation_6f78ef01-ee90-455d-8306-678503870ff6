/** 首字母大写 */
export function upperFirst(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 使用正则替换文字中的占位符，转成自己需要的文字
 * @param {string} text 原文本
 * @param {string} placeholders 占位符
 * @param {string} replaceValue 占位符的文字
 * @returns {string} 经过替换的文本
 *
 * @example
 * replacePlaceholders('请填写${1}内容', '${1}', '输入框')
 * // Returns 请填写输入框内容
 */
export function replacePlaceholders(text: string, placeholders: string, replaceValue: string) {
  return text.replace(placeholders, replaceValue);
}
