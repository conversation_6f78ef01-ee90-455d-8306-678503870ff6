<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.meeting.mapper.MeetingMapper">

    <select id="getMeetingPage" resultType="com.youlai.boot.modules.meeting.model.vo.MeetingVO">
        SELECT
            id,
            title,
            department,
            participants,
            start_time,
            end_time,
            content,
            attachments,
            meeting_type,
            create_time
        FROM
            tsz_meeting
        WHERE
            is_deleted = 0
        <if test="queryParams.title != null and queryParams.title != ''">
            AND title LIKE CONCAT('%', #{queryParams.title}, '%')
        </if>
        <if test="queryParams.department != null and queryParams.department != ''">
            AND department LIKE CONCAT('%', #{queryParams.department}, '%')
        </if>
        <if test="queryParams.meetingType != null">
            AND meeting_type = #{queryParams.meetingType}
        </if>
        <if test="queryParams.startTime != null">
            AND start_time &lt;= #{queryParams.startTime}
        </if>
        <if test="queryParams.endTime != null">
            AND end_time &gt;= #{queryParams.endTime}
        </if>
        ORDER BY create_time DESC
    </select>
    
</mapper> 