<template>
  <el-upload
    v-model:file-list="fileList"
    :class="{ 'upload-limited': props.maxFileCount && props.maxFileCount <= fileList.length }"
    :http-request="uploadFile"
    :before-upload="handleBeforeUpload"
    :before-remove="handleBeforeRemove"
    :accept="acceptType"
    :limit="props.maxFileCount"
    :multiple="false"
  >
    <el-button type="primary">上传文件</el-button>
    <template #tip>
      <div class="el-upload__tip">
        {{ props.tip }}
      </div>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import { UploadProps, UploadRawFile, UploadRequestOptions, UploadUserFile } from "element-plus";
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";

import { FileAPI } from "@/api/file";

import type { DocumentUploadProps, UploadModelType, UploadModelValue } from "./type";

const props = defineProps<DocumentUploadProps>();
const modelValue = defineModel<UploadModelValue>("modelValue");

const emit = defineEmits(["update:modelValue", "upload-error"]);

const fileList = ref<UploadUserFile[]>([]);

// 组件初始化时根据 modelValue 初始化 fileList
const initFileList = () => {
  if (!modelValue.value) {
    fileList.value = [];
    return;
  }
  if (Array.isArray(modelValue.value)) {
    fileList.value = modelValue.value
      .filter((item) => item.url)
      .map((item) => ({
        name: getFileName(item.url),
        url: item.url,
      }));
  } else {
    fileList.value = [
      {
        name: getFileName(modelValue.value.url),
        url: modelValue.value.url,
      },
    ];
  }
};

const getFileName = (url: string) => {
  if (url && url.includes("/")) {
    return url.split("/").slice(-1)[0];
  }
  return url;
};
watch(modelValue, () => {
  initFileList();
});

initFileList();

const handleBeforeRemove: UploadProps["beforeRemove"] = (uploadFile, uploadFiles) => {
  if (props.maxFileCount === 1) {
    modelValue.value = undefined;
  } else {
    const indexOfUploadfile = uploadFiles.findIndex((item) => item.url === uploadFile.url);
    console.log("uploadFiles", indexOfUploadfile, uploadFile, uploadFiles);
    if (typeof modelValue.value === "string") {
      modelValue.value = undefined;
    } else {
      modelValue.value = (modelValue.value as UploadModelType[]).filter(
        (_, index) => index !== indexOfUploadfile
      );
      return false;
    }
  }
  return true;
};

// 根据文件类型设置提示文本
const tip = computed(() => {
  if (props.tip) return props.tip;
  const maxSize = props.maxSize || 2;
  return `上传文件不能大于${maxSize}M`;
});

/**
 * 自定义文件上传
 */
async function uploadFile(options: UploadRequestOptions): Promise<any> {
  try {
    const data = await FileAPI.uploadFile(options.file);
    if (props.maxFileCount === 1) {
      modelValue.value = {
        name: data.name,
        url: data.url.replace(/\\/g, "/"),
      } as UploadModelValue;
    } else {
      modelValue.value = [
        ...((modelValue.value ?? []) as any[]),
        { name: data.name, url: data.url.replace(/\\/g, "/") },
      ];
    }
  } catch (error: any) {
    emit("upload-error", error.message);
    throw error;
  }
}

/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file: UploadRawFile) {
  if (props.acceptType == ".pdf") {
    const fileType = "." + file.type.split("/")[1];
    if (fileType != props.acceptType) {
      ElMessage.warning("请上传正确的文件");
      return false;
    }
  }
  const maxSize = (props.maxSize || 2) * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.warning(tip.value);
    return false;
  }
  return true;
}
</script>
<style scoped>
.upload-limited {
  :deep(.el-upload__tip) {
    display: none;
  }
  :deep(.el-upload--text) {
    display: none;
  }
}
</style>
