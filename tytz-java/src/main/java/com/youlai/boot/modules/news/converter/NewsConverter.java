package com.youlai.boot.modules.news.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.news.model.entity.News;
import com.youlai.boot.modules.news.model.form.NewsForm;
import com.youlai.boot.modules.news.model.vo.NewsVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * 新闻资讯对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NewsConverter {

    /**
     * 实体转换为VO
     *
     * @param entity 实体
     * @return VO
     */
    NewsVO toVO(News entity);

    /**
     * 实体分页转换为VO分页
     *
     * @param page 实体分页
     * @return VO分页
     */
    Page<NewsVO> toPageVo(Page<News> page);

    /**
     * 表单转换为实体
     *
     * @param form 表单
     * @return 实体
     */
    @Mapping(target = "attachments", ignore = true)
    News toEntity(NewsForm form);

    /**
     * 实体转换为表单
     *
     * @param entity 实体
     * @return 表单
     */
    @Mapping(target = "attachments", ignore = true)
    NewsForm toForm(News entity);
}