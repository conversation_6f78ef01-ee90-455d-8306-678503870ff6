package com.youlai.boot.modules.news.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.modules.news.model.entity.News;
import com.youlai.boot.modules.news.model.form.NewsForm;
import com.youlai.boot.modules.news.model.query.NewsQuery;
import com.youlai.boot.modules.news.model.vo.NewsVO;

/**
 * 新闻资讯服务类
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
public interface NewsService extends IService<News> {

    /**
     * 获取新闻资讯分页列表
     *
     * @param queryParams 查询参数
     * @return {@link IPage<NewsVO>} 新闻资讯分页列表
     */
    IPage<NewsVO> getNewsPage(NewsQuery queryParams);

    /**
     * 获取新闻资讯表单数据
     *
     * @param id 新闻资讯ID
     * @return 新闻资讯表单对象
     */
    NewsForm getNewsFormData(Long id);

    /**
     * 新增新闻资讯
     *
     * @param formData 新闻资讯表单对象
     * @return 是否成功
     */
    boolean saveNews(NewsForm formData);

    /**
     * 修改新闻资讯
     *
     * @param id       新闻资讯ID
     * @param formData 新闻资讯表单对象
     * @return 是否成功
     */
    boolean updateNews(Long id, NewsForm formData);

    /**
     * 删除新闻资讯
     *
     * @param ids 新闻资讯ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteNews(String ids);
}