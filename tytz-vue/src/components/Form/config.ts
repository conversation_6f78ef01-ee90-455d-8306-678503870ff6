export const defaultProps = <const>{
  /** 需要渲染的组件配置列表 */
  items: () => [],
  /** 行内表单 */
  inline: true,
  /** form-item的宽度 会被 items[item] 自身的width 覆盖 */
  itemWidth: "auto",
  /** label文字宽度 */
  labelWidth: "80px",
  /** 表单域标签的位置 */
  labelPosition: "right",
  /** 校验失败时，滚动到第一个错误表单项 */
  scrollToError: true,
  /** 显示校验错误信息 */
  showMessage: true,
  /** 是否在 rules 属性改变后立即触发一次验证 */
  validateOnRuleChange: true,
};
