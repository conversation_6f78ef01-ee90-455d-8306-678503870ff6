package com.youlai.boot.modules.problem.controller;

import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.problem.model.dto.ProblemAdoptDTO;
import com.youlai.boot.modules.problem.model.dto.ProblemInstructDTO;
import com.youlai.boot.modules.problem.model.form.ProblemForm;
import com.youlai.boot.modules.problem.model.query.ProblemQuery;
import com.youlai.boot.modules.problem.model.query.MyProblemQuery;
import com.youlai.boot.modules.problem.model.vo.ProblemExcelVO;
import com.youlai.boot.modules.problem.model.vo.ProblemVO;
import com.youlai.boot.modules.problem.service.ProblemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.alibaba.excel.EasyExcel;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 营商环境问题前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Tag(name = "营商环境问题接口")
@RestController
@RequestMapping("/api/v1/problems")
@RequiredArgsConstructor
public class ProblemController {

    private final ProblemService problemService;

    @Operation(summary = "获取营商环境问题分页列表")
    @GetMapping("/page")
    public PageResult<ProblemVO> getProblemPage(ProblemQuery queryParams) {
        return PageResult.success(problemService.getProblemPage(queryParams));
    }

    @Operation(summary = "获取领导批示分页列表")
    @GetMapping("/leaderInstruct/page")
    public PageResult<ProblemVO> getLeaderInstructPage(ProblemQuery queryParams) {
        return PageResult.success(problemService.getLeaderInstructPage(queryParams));
    }

    // 获取我提交的问题分页
    @Operation(summary = "获取我提交的问题分页列表")
    @GetMapping("/my/page")
    public PageResult<ProblemVO> getMyProblemPage(MyProblemQuery queryParams) {
        return PageResult.success(problemService.getMyProblemPage(queryParams));
    }

    @Operation(summary = "获取营商环境问题表单数据")
    @GetMapping("/{id}/form")
    public Result<ProblemForm> getProblemForm(
            @Parameter(description = "问题ID") @PathVariable Long id
    ) {
        return Result.success(problemService.getProblemFormData(id));
    }

    @Operation(summary = "新增营商环境问题")
    @PostMapping
    public Result<Void> saveProblem(@RequestBody @Valid ProblemForm formData) {
        return Result.judge(problemService.saveProblem(formData));
    }

    @Operation(summary = "修改营商环境问题")
    @PutMapping("/{id}")
    public Result<Void> updateProblem(
            @Parameter(description = "问题ID") @PathVariable Long id,
            @RequestBody @Validated ProblemForm formData
    ) {
        return Result.judge(problemService.updateProblem(id, formData));
    }

    @Operation(summary = "删除营商环境问题")
    @DeleteMapping("/{ids}")
    public Result<Void> deleteProblems(
            @Parameter(description = "问题ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        return Result.judge(problemService.deleteProblems(ids));
    }

    @Operation(summary = "采纳问题")
    @PutMapping("/adopt")
    public Result<Void> adoptProblem(
            @RequestBody ProblemAdoptDTO dto
    ) {
        return Result.judge(problemService.adoptProblem(dto));
    }

    @Operation(summary = "批示问题")
    @PutMapping("/instruct")
    public Result<Void> instructProblem(@RequestBody ProblemInstructDTO dto) {
        return Result.judge(problemService.instructProblem(dto));
    }

    /**
     * 导出营商环境问题数据
     */
    @Operation(summary = "导出营商环境问题数据")
    @PostMapping("/export")
    public void exportProblems(HttpServletResponse response, @RequestBody ProblemQuery queryParams) throws IOException {
        // 设置响应头
        String fileName = "营商环境问题数据.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        // 获取数据
        List<ProblemVO> list = problemService.getExportList(queryParams);
        List<ProblemExcelVO> excelVOList = list.stream()
                .map(item -> {
                    ProblemExcelVO excelVO = new ProblemExcelVO();
                    BeanUtils.copyProperties(item, excelVO);
                    excelVO.setIndex(Long.valueOf(list.indexOf(item) + 1));
                    return excelVO;
                }).collect(Collectors.toList());

        // 导出数据
        EasyExcel.write(response.getOutputStream(), ProblemExcelVO.class)
                .sheet("营商环境问题数据")
                .doWrite(excelVOList);
    }
}