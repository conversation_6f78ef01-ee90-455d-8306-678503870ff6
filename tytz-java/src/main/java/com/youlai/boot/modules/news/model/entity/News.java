package com.youlai.boot.modules.news.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.PublishStatusEnum;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 新闻资讯实体类
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Getter
@Setter
@TableName("tsz_news")
public class News extends BaseEntity {

    /**
     * 新闻标题
     */
    private String title;

    /**
     * 新闻内容
     */
    private String content;

    /**
     * 发布状态（WAIT: 待审核, PUSH: 审核通过, CANCEL: 审核驳回）
     */
    private PublishStatusEnum publishStatus;

    /**
     * 附件JSON数据
     */
    private String attachments;
    /**
     * 创建人ID
     */
    private Long createBy;
    /**
     * 更新人ID
     */
    private Long updateBy;
    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
}