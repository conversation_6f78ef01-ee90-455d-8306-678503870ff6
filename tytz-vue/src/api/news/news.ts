import request from "@/utils/request";

const API_PREFIX = "/api/v1/news";

/**
 * 新闻资讯分页查询对象
 */
export interface newsPageQuery extends PageQuery {
  /** 新闻标题 */
  keywords?: string;
  /** 发布状态 */
  publishStatus?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
/**获取新闻资讯分页列表 */
export interface newsPageVO {
  /** 主键 */
  id: string;

  /** 新闻标题 */
  title?: string;

  /** 新闻内容 */
  content?: string;

  /** 发布状态 */
  publishStatus?: string;

  /** 附件 */
  attachments?: string;

  /** 创建时间*/
  createTime?: string;

  /** 更新时间*/
  updateTime?: string;

  /**发布者 */
  createBy?: string;
}
/**新闻资讯分页列表 */
export const newsPageApi = (queryParams: newsPageQuery) => {
  if (!queryParams.startTime) {
    delete queryParams.startTime;
  }
  if (!queryParams.endTime) {
    delete queryParams.endTime;
  }
  return request<any, PageResult<newsPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 获取新闻资讯表单数据*/
export const newsViewApi = (id: string) => {
  return request<any, newsPageVO>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/** 新增新闻资讯*/
export const newsAddApi = (data: newsPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}`,
    method: "post",
    data: data,
  });
};

/** 修改新闻资讯*/
export const newsUpdateApi = (id: string, data: newsPageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "put",
    data: data,
  });
};

/** 删除新闻资讯*/
export const newsDeleteApi = (id: string) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "delete",
  });
};
