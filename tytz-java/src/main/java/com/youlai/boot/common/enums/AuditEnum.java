package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 数据权限枚举
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Getter
public enum AuditEnum implements IBaseEnum<Integer> {


    WAIT(0, "待审核"),
    PASS(1, "审核通过"),
    REJECT(2, "审核驳回");

    private final Integer value;

    private final String label;

    AuditEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
}
