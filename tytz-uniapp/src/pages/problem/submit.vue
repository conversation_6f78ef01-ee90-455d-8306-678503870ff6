<route lang="json5">
{
  style: {
    navigationBarTitleText: '问题报送',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { submitProblem } from '@/api/problem/submit'
import { getDictDataOptions } from '@/api/dictData'
import { onMounted } from 'vue'
import { useNewUserStoreHook } from '@/store/modules/user'

interface BusinessTypeOption {
  value: string
  label: string
  tag?: string
  children?: BusinessTypeOption[]
}

const userStore = useNewUserStoreHook()

const formData = ref({
  businessType: '',
  title: '',
  content: '',
  memberRole: userStore.role.id,
})

const businessTypeOptions = ref<BusinessTypeOption[]>([])
const businessTypeText = ref('')

// 获取营商环境类别字典数据
const getBusinessTypeOptions = async () => {
  try {
    const res = await getDictDataOptions('YSHJWTLB')
    businessTypeOptions.value =
      res.map((item) => ({
        value: item.label,
        label: item.label,
      })) || []
  } catch (error) {
    console.error('获取营商环境类别字典数据失败', error)
  }
}

// 营商环境类别选择回调
const handleBusinessTypeChange = (e: any) => {
  const index = e.detail.value
  if (index >= 0 && businessTypeOptions.value.length > 0) {
    const option = businessTypeOptions.value[index]
    formData.value.businessType = String(option.value)
    businessTypeText.value = option.label
  }
}

onMounted(() => {
  getBusinessTypeOptions()
})

const submit = async () => {
  if (!formData.value.businessType) {
    uni.showToast({
      title: '请选择营商环境类别',
      icon: 'none',
    })
    return
  }
  if (!formData.value.title) {
    uni.showToast({
      title: '请输入营商环境标题',
      icon: 'none',
    })
    return
  }
  if (!formData.value.content) {
    uni.showToast({
      title: '请输入营商环境内容描述',
      icon: 'none',
    })
    return
  }
  await submitProblem(formData.value)
  uni.navigateBack().then(() => {
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })
  })
}
</script>

<template>
  <PageContainer theme="page">
    <view class="px-4 py-3">
      <!-- 营商环境类别 -->
      <view class="mb-4">
        <view class="text-[14px] text-[#333] mb-2">
          营商环境类别
          <text class="text-#F51414">*</text>
        </view>
        <picker
          @change="handleBusinessTypeChange"
          :range="businessTypeOptions"
          range-key="label"
          class="w-full"
        >
          <view
            class="w-full h-[44px] rounded border border-solid border-#A6A6A6 box-border px-20rpx flex items-center"
          >
            <text v-if="businessTypeText" class="text-#333">{{ businessTypeText }}</text>
            <text v-else class="text-gray-400">请选择营商环境类别</text>
          </view>
        </picker>
      </view>

      <!-- 营商环境标题 -->
      <view class="mb-4">
        <view class="text-[14px] text-[#333] mb-2">
          营商环境标题
          <text class="text-#F51414">*</text>
        </view>
        <input
          v-model="formData.title"
          class="w-full h-[44px] rounded border border-solid border-#A6A6A6 box-border px-20rpx"
          placeholder="请输入营商环境标题"
          placeholder-class="text-gray-400"
        />
      </view>

      <!-- 营商环境内容描述 -->
      <view class="mb-4">
        <view class="text-[14px] text-[#333] mb-2">
          营商环境内容描述
          <text class="text-#F51414">*</text>
        </view>
        <textarea
          v-model="formData.content"
          class="w-full min-h-[200px] rounded border border-solid border-#A6A6A6 box-border p-20rpx"
          placeholder="请输入营商环境内容描述"
          placeholder-class="text-gray-400!"
          maxlength="400"
        />
      </view>

      <!-- 提交按钮 -->
      <view
        class="w-full h-[44px] bg-[#cc3333] text-white rounded text-center leading-[44px] mt-8"
        @click="submit"
      >
        提交
      </view>
    </view>
  </PageContainer>
</template>

<style scoped></style>
