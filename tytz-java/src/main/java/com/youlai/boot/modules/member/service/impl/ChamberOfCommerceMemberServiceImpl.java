package com.youlai.boot.modules.member.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper;
import com.youlai.boot.modules.member.model.query.ChamberOfCommerceMemberQuery;
import com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO;
import com.youlai.boot.modules.member.service.IChamberOfCommerceMemberService;

import lombok.RequiredArgsConstructor;

/**
 * 商会成员管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service
@RequiredArgsConstructor
public class ChamberOfCommerceMemberServiceImpl implements IChamberOfCommerceMemberService {
    private final ChamberOfCommerceMemberMapper chamberOfCommerceMemberMapper;

    @Override
    public List<ChamberOfCommerceMemberVO> getChamberOfCommerceMembers(ChamberOfCommerceMemberQuery queryParams) {
        List<ChamberOfCommerceMemberVO> result = chamberOfCommerceMemberMapper
                .getChamberOfCommerceMemberList(queryParams);
        return result;
    }
}
