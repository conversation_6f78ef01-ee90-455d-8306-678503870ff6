import { camelCase, kebabCase, merge } from "lodash-es";
import { computed, useAttrs } from "vue";

/* import type { ViewProps } from "@/components/View/type"; */
/**
 * 创建 view 组件 props
 * 作为类型提示函数使用
 */
export const useViewProps = (config: any) => {
  return config;
};

/** 获取组件被注册的slot 根据驼峰分类  */
export function useChildrenSlots(slots: any) {
  return computed(() => {
    const slot: any = {};
    for (const key in slots) {
      if (Object.prototype.hasOwnProperty.call(slots, key)) {
        const element = slots[key];
        const kebabCaseKey = kebabCase(key);
        const index = kebabCaseKey.indexOf("-");
        if (index !== -1) {
          const name = kebabCaseKey.slice(0, index);
          const slotKey = kebabCaseKey.slice(index + 1);
          if (slot[name] === void 0) {
            slot[name] = {};
          }
          slot[name][camelCase(slotKey)] = element;
        }
      }
    }
    return slot;
  });
}

/**
 * 合并props和attrs : T & P
 */
export const mergePropsAndAttrs = <P>(props: P) => {
  const attrs = useAttrs();
  return computed(() => merge({}, props, attrs));
};
