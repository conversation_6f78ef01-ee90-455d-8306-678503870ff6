package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 数据权限枚举
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Getter
public enum AdoptStatusEnum implements IBaseEnum<String> {


    WAIT("WAIT", "待采纳"),
    PASS("PASS", "已采纳"),
    REJECT("REJECT", "采纳未通过");

    private final String value;

    private final String label;

    AdoptStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
