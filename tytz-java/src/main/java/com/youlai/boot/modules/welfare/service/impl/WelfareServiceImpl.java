package com.youlai.boot.modules.welfare.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.welfare.converter.WelfareConverter;
import com.youlai.boot.modules.welfare.mapper.WelfareMapper;
import com.youlai.boot.modules.welfare.model.entity.Welfare;
import com.youlai.boot.modules.welfare.model.form.WelfareForm;
import com.youlai.boot.modules.welfare.model.query.WelfareQuery;
import com.youlai.boot.modules.welfare.model.vo.WelfareVO;
import com.youlai.boot.modules.welfare.service.WelfareService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公益信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
@RequiredArgsConstructor
public class WelfareServiceImpl extends ServiceImpl<WelfareMapper, Welfare> implements WelfareService {

    private final WelfareConverter welfareConverter;

    @Override
    public IPage<WelfareVO> getWelfarePage(WelfareQuery queryParams) {
        Page<WelfareVO> pageVO = this.baseMapper.getWelfarePage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );
        return pageVO;
    }

    @Override
    public WelfareForm getWelfareFormData(Long id) {
        Welfare entity = this.getById(id);
        Assert.notNull(entity, "公益活动不存在");
        
        // JSON转换为对象列表
        List<WelfareForm.ParticipantDTO> participants = 
                JSONUtil.toList(entity.getParticipants(), WelfareForm.ParticipantDTO.class);
        List<WelfareForm.FileDTO> attachments = 
                JSONUtil.toList(entity.getAttachments(), WelfareForm.FileDTO.class);
        
        WelfareForm form = welfareConverter.entity2Form(entity);
        form.setParticipants(participants);
        form.setAttachments(attachments);
        
        return form;
    }

    @Override
    public boolean saveWelfare(WelfareForm formData) {
        Welfare entity = welfareConverter.form2Entity(formData);
        
        // 对象列表转换为JSON
        entity.setParticipants(JSONUtil.toJsonStr(formData.getParticipants()));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        
        // 设置创建人
        entity.setCreateBy(SecurityUtils.getUserId());
        
        return this.save(entity);
    }

    @Override
    public boolean updateWelfare(Long id, WelfareForm formData) {
        Welfare entity = welfareConverter.form2Entity(formData);
        entity.setId(id);
        
        // 对象列表转换为JSON
        entity.setParticipants(JSONUtil.toJsonStr(formData.getParticipants()));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        
        // 设置更新信息
        entity.setUpdateBy(SecurityUtils.getUserId());
        entity.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(entity);
    }

    @Override
    public boolean deleteWelfares(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的公益活动ID不能为空");
        
        // 批量删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        
        return this.removeByIds(idList);
    }
} 