package com.youlai.boot.modules.work.controller;

import com.youlai.boot.modules.work.service.KeyWorkService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.youlai.boot.modules.work.model.form.KeyWorkForm;
import com.youlai.boot.modules.work.model.query.KeyWorkQuery;
import com.youlai.boot.modules.work.model.vo.KeyWorkVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * 年度重点工作前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Tag(name = "年度重点工作接口")
@RestController
@RequestMapping("/api/v1/works")
@RequiredArgsConstructor
public class KeyWorkController {

    private final KeyWorkService keyWorkService;

    @Operation(summary = "年度重点工作分页列表")
    @GetMapping("/page")
    // @PreAuthorize("@ss.hasPerm('modules.work:keywork:query')")
    public PageResult<KeyWorkVO> getKeyWorkPage(KeyWorkQuery queryParams) {
        IPage<KeyWorkVO> result = keyWorkService.getKeyWorkPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增年度重点工作")
    @PostMapping
    // @PreAuthorize("@ss.hasPerm('modules.work:keywork:add')")
    public Result<Void> saveKeyWork(@RequestBody @Valid KeyWorkForm formData) {
        boolean result = keyWorkService.saveKeyWork(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取年度重点工作表单数据")
    @GetMapping("/{id}/form")
    // @PreAuthorize("@ss.hasPerm('modules.work:keywork:edit')")
    public Result<KeyWorkForm> getKeyWorkForm(
        @Parameter(description = "重点工作ID") @PathVariable Long id
    ) {
        KeyWorkForm formData = keyWorkService.getKeyWorkFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改年度重点工作")
    @PutMapping(value = "/{id}")
    // @PreAuthorize("@ss.hasPerm('modules.work:keywork:edit')")
    public Result<Void> updateKeyWork(
            @Parameter(description = "重点工作ID") @PathVariable Long id,
            @RequestBody @Validated KeyWorkForm formData
    ) {
        boolean result = keyWorkService.updateKeyWork(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除年度重点工作")
    @DeleteMapping("/{ids}")
    // @PreAuthorize("@ss.hasPerm('modules.work:keywork:delete')")
    public Result<Void> deleteKeyWorks(
        @Parameter(description = "重点工作ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = keyWorkService.deleteKeyWorks(ids);
        return Result.judge(result);
    }
} 