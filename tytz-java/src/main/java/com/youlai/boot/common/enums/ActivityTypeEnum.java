package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 活动类型枚举
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
public enum ActivityTypeEnum implements IBaseEnum<String> {

    RESEARCH("RESEARCH", "参加商会组织的调研、视察、考察等活动"),
    //PUBLIC_WELFARE("PUBLIC_WELFARE", "参加商会组织的社会公益事业活动"),
    TRAINING("TRAINING", "参加市工商联和商会组织的培训活动"),
    MEETING("MEETING", "参加与总商会工作相关的各类会议与活动情况"),
    SUPERVISION("SUPERVISION", "受商会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动"),
    CONTRIBUTION("CONTRIBUTION", "以商会会员身份为人民群众办好事、解难题、做公益慈善等贡献（以次数计）");


    private final String value;

    private final String label;

    ActivityTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}