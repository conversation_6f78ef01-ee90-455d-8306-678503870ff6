import request from "@/utils/request";

const API_PREFIX = "/api/v1/tytzNotices";
//const API_PREFIX = "/prod-api/api/v1/tytzNotices";

/**
 * 通知公告对象
 */
export interface noticePageQuery extends PageQuery {
  /** 通知标题 */
  keywords?: string;
  /** 发布状态 */
  publishStatus?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
/**获取通知公告分页列表 */
export interface noticePageVO {
  /** 主键 */
  id: string;

  /** 新闻标题 */
  title?: string;

  /** 新闻内容 */
  content?: string;

  /** 发布状态 */
  publishStatus?: string;

  /** 附件 */
  attachments?: string;

  /** 创建时间*/
  createTime?: string;

  /** 更新时间*/
  updateTime?: string;

  /**发布者 */
  createBy?: string;
}
/**通知公告分页列表 */
export const noticePageApi = (queryParams: noticePageQuery) => {
  if (!queryParams.startTime) {
    delete queryParams.startTime;
  }
  if (!queryParams.endTime) {
    delete queryParams.endTime;
  }
  return request<any, PageResult<noticePageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 获取通知公告表单数据*/
export const noticeViewApi = (id: string) => {
  return request<any, noticePageVO>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/** 新增通知公告*/
export const noticeAddApi = (data: noticePageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}`,
    method: "post",
    data: data,
  });
};

/** 修改通知公告*/
export const noticeUpdateApi = (id: string, data: noticePageVO) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "put",
    data: data,
  });
};

/** 删除通知公告*/
export const noticeDeleteApi = (id: string) => {
  return request<any, ResponseData>({
    url: `${API_PREFIX}/${id}`,
    method: "delete",
  });
};
