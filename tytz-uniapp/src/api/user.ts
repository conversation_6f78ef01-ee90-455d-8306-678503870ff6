import request from '@/utils/request'
import { REFRESH_TOKEN } from '@/const/storage'

/**
 * 获取用户信息
 * @returns
 */
export const getUserInfo = () => {
  return request<{
    id: number
    username: string
    email: string
    roles: any[]
  }>('/api/v1/users/profile', {
    method: 'GET',
  })
}

/**
 * 刷新token
 * @returns
 */
export const refreshToken = () => {
  return request<{
    tokenType: string
    accessToken: string
    refreshToken: string
    expiresIn: number
  }>('/api/v1/auth/refresh-token', {
    method: 'POST',
    header: {
      Authorization: 'no-auth',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: {
      refreshToken: uni.getStorageSync(REFRESH_TOKEN),
    },
  })
}

/** 根据部门id查用用户列表 */
export const getUserListByDeptId = (deptId: string) => {
  return request<any>(`/api/v1/dept/${deptId}/users`, {
    method: 'GET',
  })
}
