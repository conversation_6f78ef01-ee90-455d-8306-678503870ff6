import { store } from "@/store";
import DictionaryAPI, { type DictVO, type DictData } from "@/api/system/dict";

export const useDictStore = defineStore("dict", () => {
  const dictionary = useStorage<Record<string, DictData[]>>("dictionary", {});

  const setDictionary = (dict: DictVO) => {
    dictionary.value[dict.dictCode] = dict.dictDataList;
  };

  const loadDictionaries = async () => {
    const dictList = await DictionaryAPI.getList();
    dictList.forEach(setDictionary);
  };

  const getDictionary = (dictCode: string): DictData[] => {
    return dictionary.value[dictCode] || [];
  };

  const clearDictionaryCache = () => {
    dictionary.value = {};
  };

  const updateDictionaryCache = async () => {
    clearDictionaryCache(); // 先清除旧缓存
    await loadDictionaries(); // 重新加载最新字典数据
  };
  /**
   * 获取指定字典类型的所有项目列表
   * @param key 字典类型的唯一标识符
   * @returns 返回字典项目的数组
   */
  const getDictItemToList = (key: string) => {
    return Object.values(dictionary.value[key]);
  };

  return {
    dictionary,
    setDictionary,
    loadDictionaries,
    getDictionary,
    clearDictionaryCache,
    updateDictionaryCache,
    getDictItemToList,
  };
});

export function useDictStoreHook() {
  return useDictStore(store);
}
