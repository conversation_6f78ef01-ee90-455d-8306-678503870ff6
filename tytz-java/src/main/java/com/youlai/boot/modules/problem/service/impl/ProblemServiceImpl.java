package com.youlai.boot.modules.problem.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.common.enums.AdoptStatusEnum;
import com.youlai.boot.common.enums.InstructionStatusEnum;
import com.youlai.boot.common.exception.BusinessException;
import com.youlai.boot.common.util.UserInfoUtils;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.problem.converter.ProblemConverter;
import com.youlai.boot.modules.problem.mapper.ProblemMapper;
import com.youlai.boot.modules.problem.model.dto.ProblemAdoptDTO;
import com.youlai.boot.modules.problem.model.dto.ProblemInstructDTO;
import com.youlai.boot.modules.problem.model.entity.Problem;
import com.youlai.boot.modules.problem.model.form.ProblemForm;
import com.youlai.boot.modules.problem.model.query.ProblemQuery;
import com.youlai.boot.modules.problem.model.vo.ProblemVO;
import com.youlai.boot.modules.problem.service.ProblemService;
import com.youlai.boot.system.mapper.DeptMapper;
import com.youlai.boot.system.mapper.UserMapper;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.entity.Dept;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.model.vo.UserProfileVO;
import com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import com.youlai.boot.modules.problem.model.query.MyProblemQuery;
import com.youlai.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 营商环境问题服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
@RequiredArgsConstructor
public class ProblemServiceImpl extends ServiceImpl<ProblemMapper, Problem> implements ProblemService {

    private final ProblemConverter problemConverter;

    private final DeptMapper deptMapper;

    private final UserService userService;

    private final UserMapper userMapper;

    private final ScoringRecordMapper scoringRecordMapper;

    @Override
    public IPage<ProblemVO> getProblemPage(ProblemQuery queryParams) {
        Page<Problem> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());

        LambdaQueryWrapper<Problem> wrapper = new LambdaQueryWrapper<Problem>()
                .like(StrUtil.isNotBlank(queryParams.getMemberName()), Problem::getMemberName, queryParams.getMemberName())
                .eq(queryParams.getAdoptStatus() != null, Problem::getAdoptStatus, queryParams.getAdoptStatus())
                .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Problem::getSubmitTime, queryParams.getStartTime())
                .le(StrUtil.isNotBlank(queryParams.getEndTime()), Problem::getSubmitTime, queryParams.getEndTime())
                .orderByDesc(Problem::getSubmitTime);

        Page<Problem> problemPage = this.page(page, wrapper);

        // 转换为VO
        IPage<ProblemVO> voPage = problemPage.convert(problemConverter::toVO);

        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(voPage.getRecords());

        return voPage;
    }

    @Override
    public IPage<ProblemVO> getLeaderInstructPage(ProblemQuery queryParams) {
        Page<Problem> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());

        LambdaQueryWrapper<Problem> wrapper = new LambdaQueryWrapper<Problem>()
                .like(StrUtil.isNotBlank(queryParams.getMemberName()), Problem::getMemberName, queryParams.getMemberName())
                .isNotNull(Problem::getInstructionStatus)
                .eq(queryParams.getInstructionStatus() != null, Problem::getInstructionStatus, queryParams.getInstructionStatus())
                .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Problem::getInstructionTime, queryParams.getStartTime())
                .le(StrUtil.isNotBlank(queryParams.getEndTime()), Problem::getInstructionTime, queryParams.getEndTime())
                .orderByDesc(Problem::getInstructionTime);

        Page<Problem> problemPage = this.page(page, wrapper);


        // 转换为VO
        IPage<ProblemVO> voPage = problemPage.convert(problemConverter::toVO);

        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(voPage.getRecords());

        return voPage;
    }

    @Override
    public IPage<ProblemVO> getMyProblemPage(MyProblemQuery queryParams) {
        Page<Problem> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());

        LambdaQueryWrapper<Problem> wrapper = new LambdaQueryWrapper<Problem>()
                .eq(Problem::getCreateBy, SecurityUtils.getUserId())
                .like(StrUtil.isNotBlank(queryParams.getTitle()), Problem::getTitle, queryParams.getTitle())
                .eq(queryParams.getAdoptStatus() != null, Problem::getAdoptStatus, queryParams.getAdoptStatus())
                .eq(queryParams.getInstructionStatus() != null, Problem::getInstructionStatus, queryParams.getInstructionStatus())
                .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Problem::getSubmitTime, queryParams.getStartTime())
                .le(StrUtil.isNotBlank(queryParams.getEndTime()), Problem::getSubmitTime, queryParams.getEndTime())
                .orderByDesc(Problem::getSubmitTime);

        Page<Problem> problemPage = this.page(page, wrapper);

        // 转换为VO
        IPage<ProblemVO> voPage = problemPage.convert(problemConverter::toVO);

        // 根据MemberId查询用户信息并设置MemberName和Department

        setMemberInfo(voPage.getRecords());

        return voPage;
    }


    @Override
    public ProblemForm getProblemFormData(Long id) {
        Problem entity = this.getById(id);
        Assert.notNull(entity, "营商环境问题不存在");
        // 根据MemberId查询用户信息
        if (entity.getMemberId() != null) {
            User user = userMapper.selectById(entity.getMemberId());
            if (user != null) {
                entity.setMemberName(user.getNickname());
                entity.setDepartment(user.getCompany());
            }
        }
        return problemConverter.toForm(entity);
    }

    @Override
    @Transactional
    public boolean saveProblem(ProblemForm formData) {
        Problem entity = new Problem();
        entity.setBusinessType(formData.getBusinessType());
        entity.setTitle(formData.getTitle());
        entity.setContent(formData.getContent());
        entity.setAdoptStatus(AdoptStatusEnum.WAIT);
        entity.setMemberRole(formData.getMemberRole());

        // 获取当前登录用户信息
        UserProfileVO userInfo = UserInfoUtils.getUserInfo();
        if (userInfo == null) {
            throw new BusinessException("获取当前登录用户信息失败");
        }

        // 设置会员ID（即用户ID）
        entity.setMemberId(userInfo.getId());
        // 设置会员名称（即用户昵称）
        entity.setMemberName(userInfo.getNickname());
        // 设置部门
        entity.setDepartment(userInfo.getCompany());
        entity.setSubmitTime(LocalDateTime.now());
        entity.setCreateBy(SecurityUtils.getUserId());

        // 先保存问题信息，获取问题ID
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            return false;
        }

        // 使用保存后的问题ID创建评分记录
        Long problemId = entity.getId();

        // 创建评分记录
        ScoringRecord scoringRecord = new ScoringRecord();
        scoringRecord.setMemberId(userInfo.getId()); // 设置提交人为会员ID
        scoringRecord.setProblemId(problemId); // 设置问题ID
        scoringRecord.setMemberName(userInfo.getNickname()); // 设置会员名称
        scoringRecord.setDepartment(userInfo.getCompany()); // 设置部门

        scoringRecord.setScoringType(ScoreTypeEnum.ENVIRONMENT);
        scoringRecord.setScoringDetail(formData.getBusinessType());

        // 营商环境问题报送得1分
        scoringRecord.setScore(1L);

        scoringRecord.setCreateBy(SecurityUtils.getUserId());
        scoringRecord.setCreateTime(LocalDateTime.now());
        scoringRecord.setYear(String.valueOf(LocalDateTime.now().getYear()));
        scoringRecordMapper.insert(scoringRecord);

        return true;
    }

    @Override
    @Transactional
    public boolean updateProblem(Long id, ProblemForm formData) {
        Problem entity = this.getById(id);
        Assert.notNull(entity, "营商环境问题不存在");

        Problem problem = problemConverter.toEntity(formData);
        problem.setId(id);
        problem.setUpdateBy(SecurityUtils.getUserId());
        problem.setUpdateTime(LocalDateTime.now());

        // 如果提交人发生变化，需要更新评分记录
        boolean memberIdChanged = entity.getMemberId() != null && !entity.getMemberId().equals(problem.getMemberId());

        if (memberIdChanged) {
            // 删除原有的评分记录
            scoringRecordMapper.deleteByProblemId(id);

            // 重新创建评分记录
            ScoringRecord scoringRecord = new ScoringRecord();
            scoringRecord.setMemberId(problem.getMemberId()); // 设置提交人为会员ID
            scoringRecord.setProblemId(id); // 设置问题ID

            // 根据用户ID查询用户信息
            UserBO userBO = userMapper.getUserProfile(problem.getMemberId());
            if (userBO != null) {
                // 设置会员名称和部门
                scoringRecord.setMemberName(userBO.getNickname());
                scoringRecord.setDepartment(userBO.getCompany());
            }

            scoringRecord.setScoringType(ScoreTypeEnum.ENVIRONMENT);
            scoringRecord.setScoringDetail(formData.getBusinessType());

            // 营商环境问题报送得1分
            scoringRecord.setScore(1L);

            scoringRecord.setCreateBy(SecurityUtils.getUserId());
            scoringRecord.setCreateTime(LocalDateTime.now());
            scoringRecord.setYear(String.valueOf(formData.getSubmitTime().getYear()));
            scoringRecordMapper.insert(scoringRecord);
        }

        return this.updateById(problem);
    }

    @Override
    @Transactional
    public boolean deleteProblems(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的营商环境问题ID不能为空");
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();

        // 删除相关的评分记录
        for (Long id : idList) {
            scoringRecordMapper.deleteByProblemId(id);
        }

        return this.removeByIds(idList);
    }

    @Override
    @Transactional
    public boolean adoptProblem(ProblemAdoptDTO dto) {
        Problem entity = this.getById(dto.getId());
        Assert.notNull(entity, "营商环境问题不存在");

        if (AdoptStatusEnum.REJECT.equals(dto.getAdoptStatus()) && StrUtil.isBlank(dto.getAdoptContent())) {
            throw new RuntimeException("驳回意见不能为空");
        }

        if (AdoptStatusEnum.PASS.equals(dto.getAdoptStatus())) {
            entity.setInstructionStatus(InstructionStatusEnum.WAIT);

            // 当问题采纳状态为PASS时，给提交者添加3分到评分表中
            if (entity.getMemberId() != null) {
                // 查询是否已存在评分记录
                LambdaQueryWrapper<ScoringRecord> queryWrapper = new LambdaQueryWrapper<ScoringRecord>()
                        .eq(ScoringRecord::getProblemId, entity.getId())
                        .eq(ScoringRecord::getMemberId, entity.getMemberId())
                        .eq(ScoringRecord::getScoringType, ScoreTypeEnum.ENVIRONMENT);

                ScoringRecord scoringRecord = scoringRecordMapper.selectOne(queryWrapper);

                if (scoringRecord == null) {
                    // 如果不存在，创建新的评分记录
                    scoringRecord = new ScoringRecord();
                    scoringRecord.setMemberId(entity.getMemberId()); // 设置提交人为会员ID
                    scoringRecord.setProblemId(entity.getId()); // 设置问题ID

                    // 根据用户ID查询用户信息
                    UserBO userBO = userMapper.getUserProfile(entity.getMemberId());
                    if (userBO != null) {
                        // 设置会员名称和部门
                        scoringRecord.setMemberName(userBO.getNickname());
                        scoringRecord.setDepartment(userBO.getCompany());
                    } else {
                        // 如果查询不到用户信息，则使用问题中的信息
                        scoringRecord.setMemberName(entity.getMemberName());
                        scoringRecord.setDepartment(entity.getDepartment());
                    }

                    scoringRecord.setScoringType(ScoreTypeEnum.ENVIRONMENT);
                    scoringRecord.setCreateBy(SecurityUtils.getUserId());
                    scoringRecord.setCreateTime(LocalDateTime.now());
                    // 设置年度
                    if (entity.getSubmitTime() != null) {
                        scoringRecord.setYear(String.valueOf(entity.getSubmitTime().getYear()));
                    } else {
                        scoringRecord.setYear(String.valueOf(LocalDateTime.now().getYear()));
                    }
                }

                // 营商环境问题采纳通过得3分
                scoringRecord.setScore(3L);
                scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                scoringRecord.setUpdateTime(LocalDateTime.now());

                // 保存或更新记录
                if (scoringRecord.getId() == null) {
                    scoringRecordMapper.insert(scoringRecord);
                } else {
                    scoringRecordMapper.updateById(scoringRecord);
                }
            }
        }

        entity.setAdoptBy(SecurityUtils.getUsername());
        entity.setAdoptTime(LocalDateTime.now());
        entity.setAdoptStatus(dto.getAdoptStatus());
        entity.setAdoptContent(dto.getAdoptContent());
        entity.setUpdateBy(SecurityUtils.getUserId());

        return this.updateById(entity);
    }

    @Override
    @Transactional
    public boolean instructProblem(ProblemInstructDTO dto) {
        Problem entity = this.getById(dto.getId());
        Assert.notNull(entity, "营商环境问题不存在");

        entity.setInstructionContent(dto.getInstructContent());
        entity.setInstructionBy(SecurityUtils.getUsername());
        entity.setInstructionStatus(InstructionStatusEnum.PASS);
        entity.setLeaderInstruction(dto.getLeaderInstruct());
        entity.setInstructionTime(LocalDateTime.now());
        entity.setUpdateBy(SecurityUtils.getUserId());

        // 当有领导批示时，给提交者添加分数到评分表中
        if (dto.getLeaderInstruct() != null && entity.getMemberId() != null) {
            // 查询是否已存在评分记录
            LambdaQueryWrapper<ScoringRecord> queryWrapper = new LambdaQueryWrapper<ScoringRecord>()
                    .eq(ScoringRecord::getProblemId, entity.getId())
                    .eq(ScoringRecord::getMemberId, entity.getMemberId())
                    .eq(ScoringRecord::getScoringType, ScoreTypeEnum.ENVIRONMENT);

            ScoringRecord scoringRecord = scoringRecordMapper.selectOne(queryWrapper);

            if (scoringRecord == null) {
                // 如果不存在，创建新的评分记录
                scoringRecord = new ScoringRecord();
                scoringRecord.setMemberId(entity.getMemberId()); // 设置提交人为会员ID
                scoringRecord.setProblemId(entity.getId()); // 设置问题ID

                // 根据用户ID查询用户信息
                UserBO userBO = userMapper.getUserProfile(entity.getMemberId());
                if (userBO != null) {
                    // 设置会员名称和部门
                    scoringRecord.setMemberName(userBO.getNickname());
                    scoringRecord.setDepartment(userBO.getCompany());
                } else {
                    // 如果查询不到用户信息，则使用问题中的信息
                    scoringRecord.setMemberName(entity.getMemberName());
                    scoringRecord.setDepartment(entity.getDepartment());
                }

                scoringRecord.setScoringType(ScoreTypeEnum.ENVIRONMENT);
                scoringRecord.setCreateBy(SecurityUtils.getUserId());
                scoringRecord.setCreateTime(LocalDateTime.now());

                // 设置年度
                if (entity.getSubmitTime() != null) {
                    scoringRecord.setYear(String.valueOf(entity.getSubmitTime().getYear()));
                } else {
                    scoringRecord.setYear(String.valueOf(LocalDateTime.now().getYear()));
                }
            }

            // 根据领导批示类型设置不同的分数和评分详情
            if ("Q".equals(dto.getLeaderInstruct())) {
                // 当领导批示为 "Q" 时，添加 6 分
                scoringRecord.setScore(6L);
            } else {
                // 当领导批示为 "S" 时，添加 7 分
                scoringRecord.setScore(7L);
            }

            scoringRecord.setUpdateBy(SecurityUtils.getUserId());
            scoringRecord.setUpdateTime(LocalDateTime.now());

            // 保存或更新记录
            if (scoringRecord.getId() == null) {
                scoringRecordMapper.insert(scoringRecord);
            } else {
                scoringRecordMapper.updateById(scoringRecord);
            }
        }

        return this.updateById(entity);
    }

    @Override
    public List<ProblemVO> getExportList(ProblemQuery queryParams) {
        List<Problem> problemList;

        // 如果提供了ID参数，则根据ID查询
        if (StrUtil.isNotBlank(queryParams.getIds())) {
            // 将逗号分隔的ID字符串转换为集合
            List<Long> idList = Arrays.stream(queryParams.getIds().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            // 根据ID集合查询
            problemList = this.listByIds(idList);
        } else {
            // 检查是否有其他查询条件
            boolean hasConditions = StrUtil.isNotBlank(queryParams.getMemberName()) ||
                    StrUtil.isNotBlank(queryParams.getTitle()) ||
                    StrUtil.isNotBlank(queryParams.getBusinessType()) ||
                    StrUtil.isNotBlank(queryParams.getDepartment()) ||
                    queryParams.getAdoptStatus() != null ||
                    queryParams.getInstructionStatus() != null ||
                    StrUtil.isNotBlank(queryParams.getStartTime()) ||
                    StrUtil.isNotBlank(queryParams.getEndTime());

            if (hasConditions) {
                // 有查询条件，根据条件查询
                LambdaQueryWrapper<Problem> wrapper = new LambdaQueryWrapper<Problem>()
                        .like(StrUtil.isNotBlank(queryParams.getTitle()), Problem::getTitle, queryParams.getTitle())
                        .like(StrUtil.isNotBlank(queryParams.getBusinessType()), Problem::getBusinessType, queryParams.getBusinessType())
                        .like(StrUtil.isNotBlank(queryParams.getMemberName()), Problem::getMemberName, queryParams.getMemberName())
                        .like(StrUtil.isNotBlank(queryParams.getDepartment()), Problem::getDepartment, queryParams.getDepartment())
                        .eq(queryParams.getAdoptStatus() != null, Problem::getAdoptStatus, queryParams.getAdoptStatus())
                        .eq(queryParams.getInstructionStatus() != null, Problem::getInstructionStatus, queryParams.getInstructionStatus())
                        .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Problem::getSubmitTime, queryParams.getStartTime())
                        .le(StrUtil.isNotBlank(queryParams.getEndTime()), Problem::getSubmitTime, queryParams.getEndTime())
                        .orderByDesc(Problem::getSubmitTime);

                problemList = this.list(wrapper);
            } else {
                // 没有查询条件，导出全部数据
                LambdaQueryWrapper<Problem> wrapper = new LambdaQueryWrapper<Problem>()
                        .orderByDesc(Problem::getSubmitTime);

                problemList = this.list(wrapper);
            }
        }

        // 转换为VO
        List<ProblemVO> voList = problemList.stream()
                .map(problemConverter::toVO)
                .collect(Collectors.toList());

        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(voList);

        return voList;
    }

    /**
     * 根据MemberId查询用户信息并设置MemberName和Department
     * 无论实体中是否已经有值，都会使用从用户表中查询到的最新信息
     *
     * @param voList 问题VO列表
     */
    private void setMemberInfo(List<ProblemVO> voList) {
        if (voList == null || voList.isEmpty()) {
            return;
        }

        // 遍历每个问题VO
        for (ProblemVO vo : voList) {
            try {
                // 直接使用memberId
                Long memberId = null;
                if (vo.getMemberId() != null && !vo.getMemberId().isEmpty()) {
                    memberId = Long.parseLong(vo.getMemberId());
                }

                // 如果memberId为空，则不进行查询，直接使用数据库中已存储的值
                if (memberId == null) {
                    continue; // 跳过当前循环，不进行用户信息的查询
                }

                // 查询用户信息
                UserBO userBO = userMapper.getUserProfile(memberId);

                if (userBO != null) {
                    // 强制覆盖实体中的值，使用从用户表中查询到的最新信息
                    vo.setMemberName(userBO.getNickname()); // 使用昵称作为会员名称
                    vo.setDepartment(userBO.getCompany()); // 使用企业单位名称作为部门
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }
}