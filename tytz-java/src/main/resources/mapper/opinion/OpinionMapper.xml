<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.opinion.mapper.OpinionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youlai.boot.modules.opinion.model.entity.Opinion">
        <id column="id" property="id"/>
        <result column="content" property="content"/>
        <result column="submit_by" property="submitBy"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>
    
    <!-- 通用查询结果列 -->

    <!-- 在这里可以添加自定义的SQL查询 -->
    
</mapper> 