<route lang="json5">
{
  style: {
    navigationBarTitleText: '修改密码',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { updatePassword } from '@/api/person/password'
import { useNewUserStoreHook } from '@/store/modules/user'

defineOptions({
  name: 'PersonPassword',
})

const formDataList = [
  {
    title: '原密码',
    prop: 'oldPassword',
  },
  {
    title: '新密码',
    prop: 'newPassword',
  },
  {
    title: '密码',
    prop: 'confirmPassword',
  },
]

const formData = reactive(Object.fromEntries(formDataList.map((item) => [item.prop, ''])))

const handleSubmit = async () => {
  for (const key of formDataList) {
    if (!formData[key.prop]) {
      uni.showToast({
        title: '请输入' + key.title,
        icon: 'none',
      })
      return
    }
  }
  if (formData.oldPassword === formData.newPassword) {
    uni.showToast({
      title: '新密码不能与原密码相同',
      icon: 'none',
    })
    return
  }
  if (formData.newPassword !== formData.confirmPassword) {
    uni.showToast({
      title: '新密码与确认密码不一致',
      icon: 'none',
    })
    return
  }

  await updatePassword({
    oldPassword: formData.oldPassword,
    newPassword: formData.newPassword,
    confirmPassword: formData.confirmPassword,
  })
  useNewUserStoreHook().clearUserInfo()

  uni.redirectTo({
    url: '/pages/login/index',
    success: () => {
      uni.showToast({
        title: '密码已修改，请重新登录',
        icon: 'success',
      })
    },
  })
}
</script>

<template>
  <PageContainer>
    <view class="mb-4" v-for="item in formDataList" :key="item.prop">
      <view class="text-[14px] text-[#333] mb-2">
        {{ item.title }}
        <text class="text-#F51414">*</text>
      </view>
      <input
        password
        v-model="formData[item.prop]"
        class="w-full h-[44px] rounded border border-solid border-#A6A6A6 box-border px-20rpx"
        :placeholder="'请输入' + item.title"
        placeholder-class="text-gray-400"
      />
    </view>
    <view
      class="w-full h-[44px] bg-[#cc3333] text-white rounded text-center leading-[44px] mt-8"
      @click="handleSubmit"
    >
      提交
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped></style>
