import type { FormItemRule, FormProps as ElFormProps } from "element-plus";

import type { ComponentTypeMap } from "@/components/CustomComponent/type";

import type { DIALOG_TYPE } from "./config";

export interface DialogPageProps<T extends object> {
  /** 弹窗是否可见 */
  visible?: boolean;
  "onUpdate:visible"?: (visible: boolean) => void;
  /** 弹窗标题 */
  title?: string;
  /** 弹窗宽度 */
  width?: string | number;
  /** 表单配置 */
  formItems?: FormItemsUnionTypeList | any;
  /** 弹窗类型 */
  type?: (typeof DIALOG_TYPE)[keyof typeof DIALOG_TYPE];
  /** 初始数据服务，返回值会覆盖 initialData */
  initialDataService?: (originalData: T) => Promise<any>;
  /** 初始数据，如果指定了 initialDataService，则此值则用于传递给 initialDataService */
  initialData?: Partial<T>;
  /** 提交前回调 */
  onBeforeSubmit?: (data: T) => void;
  /** 提交请求 */
  service?: (data: T) => Promise<any>;
  /** 获取详情请求 */
  detailService?: (id: string | number) => Promise<T>;
  /** 成功回调 */
  onSuccess?: (data: any) => void;
  /** 取消回调 */
  onCancel?: () => void;
  /** 是否可以通过点击 modal 关闭 Dialog */
  closeOnClickModal?: boolean;
  /* * 是否可以通过按下 ESC 关闭 Dialog */
  closeOnPressEscape?: boolean;
}

export interface FormProps extends Partial<ElFormProps> {
  items?: FormItemsUnionTypeList;
  /**
   * form-item 占据的宽度
   * 会被 items[width] 覆盖
   */
  itemWidth?: string | number;
}

/** form items 联合类型组成的数组 */
export type FormItemsUnionTypeList = FormItemUnionType[];

/** form items 联合类型 */
export type FormItemUnionType = {
  [K in keyof ComponentTypeMap]: {
    is?: K | "input";
    props?: ComponentTypeMap[K] & { placeholder?: string };
    /** FormItem组件 props */
    formItem: {
      prop: string;
      label: string;
      showMessage?: boolean;
      rules?: FormItemRule[] | any;
      required?: boolean;
    };
    hidden?: boolean;
    /** 默认值 */
    initialValue?: any;
    /** 提交时格式化数据 在调用 form.validate 方法通过后调用 */
    formatter?: (v?: any) => any;
    /** form-item 占据的宽度 */
    width?: string | number;
    /** 是否覆盖整个表单项 */
    slotOnFormItem?: boolean;
  };
}[keyof ComponentTypeMap];
