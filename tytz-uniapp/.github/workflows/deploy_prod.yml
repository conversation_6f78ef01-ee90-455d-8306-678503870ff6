name: 🚨部署到正式环境
on:
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 10
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Build project
      run: pnpm run build:h5
          
    - name: Upload build files
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        source: "dist/build/h5/"
        target: "/opt/tytz_prod/mobile-uni/upload"
        
    - name: Finish deployment
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        script: |
          cd /opt/tytz_prod/mobile-uni
          rm -rf dist
          cp -r upload/dist/build/h5 .
          rm -rf upload
          mv h5 dist
          cd ..
          docker compose down tytz-prod-mobile-uni
          docker compose up -d tytz-prod-mobile-uni