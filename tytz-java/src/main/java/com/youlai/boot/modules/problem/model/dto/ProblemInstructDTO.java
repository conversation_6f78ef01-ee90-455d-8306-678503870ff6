package com.youlai.boot.modules.problem.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ProblemInstructDTO {
    @Schema(description = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    @Schema(description = "领导批示")
    @NotNull(message = "领导批示不能为空")
    private String leaderInstruct;

    @Schema(description = "批示内容")
    private String instructContent;
}
