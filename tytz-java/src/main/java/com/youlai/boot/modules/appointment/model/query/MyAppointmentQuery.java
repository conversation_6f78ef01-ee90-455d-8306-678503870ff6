package com.youlai.boot.modules.appointment.model.query;
import com.youlai.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询我的约见申请
 *
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "约见申请查询对象")
@Data
public class MyAppointmentQuery extends BasePageQuery {

        @Schema(description = "内容关键字")
        private String keyword;

        @Schema(description = "开始时间")
        private String startTime;

        @Schema(description = "结束时间")
        private String endTime;
}
