package com.youlai.boot.modules.welfare.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 公益信息表单对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Schema(description = "公益信息表单对象")
public class WelfareForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "公益活动ID")
    private Long id;

    @Schema(description = "公益活动名称")
    @NotBlank(message = "公益活动名称不能为空")
    @Size(max=100, message="公益活动名称长度不能超过100个字符")
    private String title;

    @Schema(description = "主办单位")
    @NotBlank(message = "主办单位不能为空")
    @Size(max=50, message="主办单位长度不能超过50个字符")
    private String department;

    @Schema(description = "参与人员")
    @NotNull(message = "参与人员不能为空")
    private List<ParticipantDTO> participants;

    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    private LocalDateTime endTime;

    @Schema(description = "活动内容")
    private String content;

    @Schema(description = "附件")
    private List<FileDTO> attachments;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ParticipantDTO {
        @Schema(description = "组织名称")
        private String orgName;

        @Schema(description = "参与人员")
        private String personName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileDTO {
        @Schema(description = "文件url")
        private String url;

        @Schema(description = "文件类型")
        private String type;
        
        @Schema(description = "文件名")
        private String name;
    }
} 