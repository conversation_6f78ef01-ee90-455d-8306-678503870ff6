package com.youlai.boot.modules.work.model.entity;

import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.WorkTypeEnum;

/**
 * 年度重点工作实体对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@TableName("tsz_key_work")
public class KeyWork extends BaseEntity {

    /**
     * 年度
     */
    private Integer year;

    /**
     * 工作名称
     */
    private String workName;

    /**
     * 工作类型
     */
    private WorkTypeEnum workType;
    
    /**
     * 负责部门
     */
    //private String department;
    
    /**
     * 负责人员JSON数据
     */
    private String participants;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 工作内容
     */
    private String workContent;
    
    /**
     * 附件JSON数据，包含文件名、路径等信息
     */
    private String attachments;
    
    /**
     * 创建人ID
     */
    private Long createBy;
    
    /**
     * 更新人ID
     */
    private Long updateBy;
    
    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
} 