<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EditableItem, EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import displayableDialog from "@/components/EntityCrud/DisplayableDialog/index.vue";
import type { DisplayableItem } from "@/components/EntityCrud/DisplayableDialog/type";
import {
  activityPageVO,
  activityPageApi,
  activityAddApi,
  activityUpdateApi,
  activityDeleteApi,
  activityViewApi,
} from "@/api/rating/activity";
//import UserAPI from "@/api/system/user";
import DeptAPI from "@/api/system/dept";
import dayjs from "dayjs";
//PUBLIC_WELFARE: "参加商会组织的社会公益事业活动",
const typeEnum = {
  RESEARCH: "参加商会组织的调研、视察、考察等活动及商会组织的社会公益事业活动等",
  TRAINING: "参加市工商联和商会组织的培训活动",
  MEETING: "参加与总商会工作相关的各类会议与活动情况",
  SUPERVISION:
    "受商会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动",
  CONTRIBUTION: "以商会会员身份为人民群众办好事、解难题、做公益慈善等贡献(以次数计)",
};

const entityCrudRef = ref<any>(null);

const dialogVisible = ref(false);
const detailData = ref<activityPageVO | null>(null);
const displayItems: DisplayableItem[] = [
  {
    label: "活动名称",
    prop: "title",
    type: "text",
  },
  {
    label: "活动类型",
    prop: "activityType",
    type: "text",
    formatter: (value: any) => {
      const key = value as keyof typeof typeEnum;
      return typeEnum[key];
    },
  },
  {
    label: "参加人员",
    prop: "participants",
    type: "text",
  },
  {
    label: "活动时间",
    prop: "activityTime",
    type: "date-range",
  },
  {
    label: "活动内容",
    prop: "content",
    type: "rich-text",
  },
  {
    label: "附件",
    prop: "attachments",
    type: "documents",
  },
];
const getActivityFormItems = (): EditableItem<activityPageVO> => {
  return {
    id: {
      type: "id",
    },
    title: {
      type: "input",
      label: "活动名称",
      required: true,
    },
    activityType: {
      type: "select",
      label: "活动类型",
      options: getEnumOptions(typeEnum),
      required: true,
    },
    participants: {
      type: "select",
      label: "参加人员",
      required: true,
    },
    activityTime: {
      type: "date-picker",
      label: "活动时间",
      required: true,
    },
    content: {
      type: "editor",
      label: "活动内容",
      required: true,
    },
    attachments: {
      type: "multiple-document",
      label: "附件",
      maxFileCount: 10,
      tip: "(注：最多上传10个文件，可上传.pdf、.jpg、.png、.xlsx、.docx、.xls、.doc格式)",
    },
  };
};

const config: EntityCrudProps<activityPageVO> = {
  entityName: "activity",
  displayName: "活动信息",
  hasIndexColumn: true,
  filterFormItems: {
    title: {
      type: "input",
      label: "活动名称",
    },
    activityType: {
      type: "select",
      label: "活动类型",
      options: getEnumOptions(typeEnum),
    },
    activityTime: {
      type: "date-range",
      label: "活动时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  rowOperations: [
    {
      label: "详情",
      type: "link",
      displayIndex: -1,
      actionService: async (row) => {
        const result: activityPageVO = await activityViewApi(row.id);
        result.activityType = typeEnum[result.activityType as keyof typeof typeEnum];
        result.activityTime = [result.startTime, result.endTime];
        result.participants = result.participants
          .map(
            (participant: any) => participant.businessName + "(" + participant.businessMember + ")"
          )
          .join("，");
        detailData.value = result;
        dialogVisible.value = true;
      },
    },
  ],
  createButtonLabel: "新增",
  tableColumns: {
    title: "活动名称",
    activityType: {
      label: "活动类型",
      formatter: (value: any) => {
        const key = value as keyof typeof typeEnum;
        return typeEnum[key];
      },
    },
    startTime: {
      label: "活动开始时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    endTime: {
      label: "活动结束时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
  },

  listFetchService: async (params) => {
    const result: any = await activityPageApi(params);
    result.list.forEach((item: any) => {
      item.attachments = item.attachments ? JSON.parse(item.attachments) : [];
      item.activityTime = [item.startTime, item.endTime];
      item.participants = item.participants
        ? JSON.parse(item.participants).map((participant: any) => participant.id.toString()) // 确保是字符串格式
        : [];
    });
    return Promise.resolve(result);
  },

  createFormItems: getActivityFormItems(),
  createService: (newRecord: activityPageVO) => {
    if (newRecord.activityTime && newRecord.activityTime.length >= 2) {
      const [startDate, endDate] = newRecord.activityTime;
      newRecord.startTime = `${startDate} 00:00:00`;
      newRecord.endTime = `${endDate} 23:59:59`;
    }
    if (Array.isArray(newRecord.participants)) {
      newRecord.participants = newRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete newRecord.activityTime;
    return activityAddApi(newRecord);
  },
  useCreateFormItemsAsUpdate: true,
  updateService: (updateRecord: activityPageVO) => {
    if (updateRecord.activityTime && updateRecord.activityTime.length >= 2) {
      const [startDate, endDate] = updateRecord.activityTime;
      updateRecord.startTime = startDate.includes("T")
        ? startDate.replace("T", " ")
        : `${startDate} 00:00:00`;
      updateRecord.endTime = endDate.includes("T")
        ? endDate.replace("T", " ")
        : `${endDate} 23:59:59`;
    }
    if (Array.isArray(updateRecord.participants)) {
      updateRecord.participants = updateRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete updateRecord.activityTime;
    return activityUpdateApi(updateRecord.id, updateRecord);
  },
  deleteService: (row: activityPageVO) => {
    return activityDeleteApi(row.id);
  },
};
const entityCrudProps = defineEntityCrud(config);

const memberOptions = ref<{ label: string; value: string }[]>([]);

const getMemberOptions = async () => {
  await DeptAPI.getUsersByDeptId("5").then((res: any) => {
    if (Array.isArray(res)) {
      memberOptions.value = res.map((item: any) => {
        return {
          label: `${item.nickname}（${item.company ? item.company : "-"}）-${item.deptName}`,
          value: item.id,
        };
      });
    }
  });
};

const selectParticipants = ref<any>("");
const handleChangeSelect = (value: any) => {
  selectParticipants.value = value;
};
getMemberOptions();
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #participants="{ formData, props, change }">
        <el-select
          v-model="formData.participants"
          placeholder="请选择商会成员"
          multiple
          @change="handleChangeSelect"
        >
          <el-option
            v-for="option in memberOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
    </EntityCrud>
    <displayableDialog
      v-model:visible="dialogVisible"
      :display-data="detailData"
      :display-items="displayItems"
      title="详情"
      width="800px"
    />
  </div>
</template>

<style lang="scss" scoped></style>
