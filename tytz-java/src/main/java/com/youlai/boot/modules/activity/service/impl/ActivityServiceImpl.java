package com.youlai.boot.modules.activity.service.impl;

import cn.hutool.json.JSONUtil;
import com.youlai.boot.common.enums.ActivityTypeEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.scoringrecord.mapper.ScoringRecordMapper;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.model.vo.UserPageVO;
import com.youlai.boot.system.service.UserService;
import com.youlai.boot.system.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.modules.activity.mapper.ActivityMapper;
import com.youlai.boot.modules.activity.service.ActivityService;
import com.youlai.boot.modules.activity.model.entity.Activity;
import com.youlai.boot.modules.activity.model.form.ActivityForm;
import com.youlai.boot.modules.activity.model.query.ActivityQuery;
import com.youlai.boot.modules.activity.model.vo.ActivityVO;
import com.youlai.boot.modules.activity.converter.ActivityConverter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

/**
 * 活动管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Service
@RequiredArgsConstructor
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements ActivityService {

    private final ActivityConverter activityConverter;

    private final UserService userService;

    private final ScoringRecordMapper scoringRecordMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 获取活动管理分页列表
     *
     * @param queryParams 查询参数
     * @return 活动分页列表
     */
    @Override
    public IPage<ActivityVO> getActivityPage(ActivityQuery queryParams) {
        Page<ActivityVO> pageVO = this.baseMapper.getActivityPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );
        return pageVO;
    }

    /**
     * 获取活动管理表单数据
     *
     * @param id 活动ID
     * @return 表单数据
     */
    @Override
    public ActivityForm getActivityFormData(Long id) {
        Activity entity = this.getById(id);
        Assert.notNull(entity, "活动不存在");

        // 解析参与人员ID数组
        List<Map> participantMaps = JSONUtil.toList(entity.getParticipants(), Map.class);
        List<Long> participantIds = participantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 查询用户信息，使用UserBO对象
        List<UserBO> userBOs = getUserBOListByIds(participantIds);

        // 转换为参与者ID DTO
        List<ActivityForm.ParticipantDTO> participantDTOs = userBOs.stream()
                .map(userBO -> {
                    return ActivityForm.ParticipantDTO.builder()
                            .id(userBO.getId())
                            .businessName(userBO.getNickname())
                            .businessMember(userBO.getCompany() + " - " + userBO.getDeptName())
                            .build();
                })
                .collect(Collectors.toList());


        List<ActivityForm.FileDTO> attachments = JSONUtil.toList(entity.getAttachments(), ActivityForm.FileDTO.class);

        ActivityForm form = activityConverter.toForm(entity);
        form.setParticipants(participantDTOs); // 设置参与者详细信息
        form.setAttachments(attachments);
        return form;
    }

    /**
     * 新增活动
     *
     * @param formData 活动表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveActivity(ActivityForm formData) {
        Activity entity = activityConverter.toEntity(formData);

        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());

        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setCreateBy(SecurityUtils.getUserId());

        // 先保存活动信息，获取活动ID
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            return false;
        }

        // 使用保存后的活动ID创建评分记录
        Long activityId = entity.getId();

        // 为每个参与者创建评分记录
        formData.getParticipants().stream().forEach(participant -> {
            ScoringRecord scoringRecord = new ScoringRecord();
            scoringRecord.setMemberId(participant.getId());
            scoringRecord.setActivityId(activityId); // 设置活动ID

            // 根据用户ID查询用户信息
            UserBO userBO = userMapper.getUserProfile(participant.getId());
            if (userBO != null) {
                // 设置会员名称和部门
                scoringRecord.setMemberName(userBO.getNickname());
                scoringRecord.setDepartment(userBO.getCompany());
            } else {
                // 如果查询不到用户信息，则使用参与者对象中的信息
                scoringRecord.setMemberName(participant.getBusinessName());
                scoringRecord.setDepartment(participant.getBusinessMember());
            }

            scoringRecord.setScoringType(ScoreTypeEnum.ACTIVITY);
            scoringRecord.setScoringDetail(formData.getActivityType().getLabel());

            // 根据活动类型设置分数
            if (ActivityTypeEnum.RESEARCH.equals(formData.getActivityType())) {
                // 参加商会组织的调研、视察、考察等活动得2分
                scoringRecord.setScore(1L);
            } else if (ActivityTypeEnum.TRAINING.equals(formData.getActivityType())) {
                // 参加市工商联和商会组织的培训活动得2分
                scoringRecord.setScore(2L);
            } else if (ActivityTypeEnum.MEETING.equals(formData.getActivityType())) {
                // 参加与总商会工作相关的各类会议与活动情况得2分
                scoringRecord.setScore(1L);
            } else if (ActivityTypeEnum.SUPERVISION.equals(formData.getActivityType())) {
                // 受商会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动得2分
                scoringRecord.setScore(1L);
            } else if (ActivityTypeEnum.CONTRIBUTION.equals(formData.getActivityType())) {
                // 以商会会员身份为人民群众办好事、解难题、做公益慈善等贡献（以次数计）得2分
                scoringRecord.setScore(3L);
            }

            scoringRecord.setCreateBy(SecurityUtils.getUserId());
            scoringRecord.setCreateTime(LocalDateTime.now());
            scoringRecord.setYear(String.valueOf(formData.getStartTime().getYear()));
            scoringRecordMapper.insert(scoringRecord);
        });

        return true;
    }

    /**
     * 更新活动
     *
     * @param id 活动ID
     * @param formData 活动表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateActivity(Long id, ActivityForm formData) {
        // 获取原活动信息
        Activity oldActivity = this.getById(id);
        Assert.notNull(oldActivity, "活动不存在");

        Activity entity = activityConverter.toEntity(formData);
        entity.setId(id);

        // 将参与者对象转换为包含id的对象数组
        List<Map<String, Object>> participantMaps = formData.getParticipants().stream()
                .map(participant -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", participant.getId());
                    return map;
                })
                .collect(Collectors.toList());

        entity.setParticipants(JSONUtil.toJsonStr(participantMaps));
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setUpdateBy(SecurityUtils.getUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 解析原参与人员ID数组
        List<Map> oldParticipantMaps = JSONUtil.toList(oldActivity.getParticipants(), Map.class);
        List<Long> oldParticipantIds = oldParticipantMaps.stream()
                .map(map -> Long.parseLong(map.get("id").toString()))
                .collect(Collectors.toList());

        // 解析新参与人员ID数组
        List<Long> newParticipantIds = formData.getParticipants().stream()
                .map(participant -> participant.getId())
                .collect(Collectors.toList());

        // 如果活动类型发生变化或者参与人员发生变化，需要更新评分记录
        boolean activityTypeChanged = !oldActivity.getActivityType().equals(entity.getActivityType());
        boolean participantsChanged = !oldParticipantIds.equals(newParticipantIds);

        if (activityTypeChanged || participantsChanged) {
            // 删除原有的评分记录
            scoringRecordMapper.deleteByActivityId(id);

            // 重新创建评分记录
            formData.getParticipants().stream().forEach(participant -> {
                ScoringRecord scoringRecord = new ScoringRecord();
                scoringRecord.setMemberId(participant.getId());
                scoringRecord.setActivityId(id); // 设置活动ID

                // 根据用户ID查询用户信息
                UserBO userBO = userMapper.getUserProfile(participant.getId());
                if (userBO != null) {
                    // 设置会员名称和部门
                    scoringRecord.setMemberName(userBO.getNickname());
                    scoringRecord.setDepartment(userBO.getCompany());
                } else {
                    // 如果查询不到用户信息，则使用参与者对象中的信息
                    scoringRecord.setMemberName(participant.getBusinessName());
                    scoringRecord.setDepartment(participant.getBusinessMember());
                }

                scoringRecord.setScoringType(ScoreTypeEnum.ACTIVITY);
                scoringRecord.setScoringDetail(formData.getActivityType().getLabel());

                // 根据活动类型设置分数
                if (ActivityTypeEnum.RESEARCH.equals(formData.getActivityType())) {
                    // 参加商会组织的调研、视察、考察等活动得2分
                    scoringRecord.setScore(1L);
                } else if (ActivityTypeEnum.TRAINING.equals(formData.getActivityType())) {
                    // 参加市工商联和商会组织的培训活动得2分
                    scoringRecord.setScore(2L);
                } else if (ActivityTypeEnum.MEETING.equals(formData.getActivityType())) {
                    // 参加与总商会工作相关的各类会议与活动情况得2分
                    scoringRecord.setScore(1L);
                } else if (ActivityTypeEnum.SUPERVISION.equals(formData.getActivityType())) {
                    // 受商会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动得2分
                    scoringRecord.setScore(1L);
                } else if (ActivityTypeEnum.CONTRIBUTION.equals(formData.getActivityType())) {
                    // 以商会会员身份为人民群众办好事、解难题、做公益慈善等贡献（以次数计）得2分
                    scoringRecord.setScore(3L);
                }

                scoringRecord.setUpdateBy(SecurityUtils.getUserId());
                scoringRecord.setUpdateTime(LocalDateTime.now());
                scoringRecord.setYear(String.valueOf(formData.getStartTime().getYear()));
                scoringRecordMapper.insert(scoringRecord);
            });
        }

        return this.updateById(entity);
    }

    /**
     * 删除活动
     *
     * @param ids 活动ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteActivities(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的活动数据为空");
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();

        // 删除相关的评分记录
        for (Long id : idList) {
            scoringRecordMapper.deleteByActivityId(id);
        }

        return this.removeByIds(idList);
    }

    /**
     * 根据用户ID列表获取UserBO列表
     *
     * @param userIds 用户ID列表
     * @return UserBO列表
     */
    @Override
    public List<UserBO> getUserBOListByIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 直接使用UserService的listByIds方法获取User实体
        List<User> users = userService.listByIds(userIds);

        // 使用UserMapper获取用户信息，包含部门名称等
        List<UserBO> userBOs = new ArrayList<>();
        for (User user : users) {
            // 获取用户个人信息，包含部门名称
            UserBO userBO = userMapper.getUserProfile(user.getId());
            if (userBO != null) {
                userBOs.add(userBO);
            }
        }

        return userBOs;
    }
}