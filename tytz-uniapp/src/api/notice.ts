import request from '@/utils/request'

/**
 * DataTyTzNoticeVO
 */
export interface DataTyTzNoticeVO {
  list?: TyTzNoticeVO[]
  total?: number
  [property: string]: any
}

/**
 * 通知公告视图对象
 *
 * TyTzNoticeVO
 */
export interface TyTzNoticeVO {
  /**
   * 附件JSON数据，包含文件名、路径等信息
   */
  attachments?: string
  /**
   * 通知内容
   */
  content?: string
  /**
   * 创建人
   */
  createBy?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * id
   */
  id?: number
  /**
   * 发布状态（0: 未发布, 1: 已发布, 2: 待发布）
   */
  publishStatus?: PublishStatus
  /**
   * 通知标题
   */
  title?: string
  [property: string]: any
}

/**
 * 发布状态（0: 未发布, 1: 已发布, 2: 待发布）
 */
export enum PublishStatus {
  Cancel = 'CANCEL',
  Push = 'PUSH',
  Wait = 'WAIT',
}

/**
 * 获取通知公告列表
 */
export const getNoticeList = (params: { page: number; pageSize: number }) => {
  return request<DataTyTzNoticeVO>('/api/v1/tytzNotices/page', {
    method: 'GET',
    data: {
      page: params.page,
      pageSize: params.pageSize,
      publishStatus: PublishStatus.Push,
    },
  })
}
