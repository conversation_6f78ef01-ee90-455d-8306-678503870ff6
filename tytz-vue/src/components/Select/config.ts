/** props 默认值 */
export const defaultProps = {
  allowCreate: void 0,
  automaticDropdown: void 0,
  clearable: void 0,
  collapseTags: void 0,
  collapseTagsTooltip: void 0,
  defaultFirstOption: void 0,
  disabled: void 0,
  filterable: void 0,
  /**	下拉框的宽度是否与输入框相同 */
  fitInputWidth: void 0,
  loading: void 0,
  persistent: void 0,
  multiple: void 0,
  remote: void 0,
  remoteShowSuffix: void 0,
  /**
   * 当 multiple 和 filterable被设置为 true 时，
   * 是否在选中一个选项后保留当前的搜索关键词
   * */
  reserveKeyword: true,
  /**
   * 是否将弹窗容器插入至 body 元素上。
   * 嵌套的弹窗等会拥有 better-scroll
   * */
  teleported: true,
  /**
   * 是否触发表单验证
   * */
  validateEvent: true,
  options: () => [],
};
