<route lang="json5">
{
  style: {
    navigationBarTitleText: '工作中心',
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
defineOptions({
  name: 'Work',
})

const modules = Object.fromEntries(
  Object.entries(
    import.meta.glob<{
      default: string
    }>('/src/static/images/work/*.png', {
      eager: true,
    }),
  ).map(([key, value]) => {
    return [key.split('/').pop()?.split('.').shift(), value.default]
  }),
)

const menus = [
  {
    name: '营商环境',
    children: [
      {
        name: '问题报送',
        path: '/pages/problem/submit',
      },
      {
        name: '已报问题',
        path: '/pages/problem/list/index',
      },
    ],
  },
  {
    name: '商会会员意见征集',
    children: [
      {
        name: '意见征集',
        path: '/pages/opinion/collect',
      },
      {
        name: '意见汇总',
        path: '/pages/opinion/summary/index',
      },
    ],
  },
  {
    name: '约见管理',
    children: [
      {
        name: '约见申请',
        path: '/pages/meet/apply',
      },
      {
        name: '约见信息查询',
        path: '/pages/meet/info',
      },
    ],
  },
  {
    name: '新闻资讯',
    children: [
      {
        name: '新闻资讯',
        path: '/pages/news/index',
      },
    ],
  },
  {
    name: '通知公告',
    children: [
      {
        name: '通知公告',
        path: '/pages/notice/index',
      },
    ],
  },
] as const

type MenuPaths = (typeof menus)[number]['children'][number]['path']

const handleClick = (path: MenuPaths) => {
  // 放行的页面
  const whiteList = [
    '/pages/problem/submit',
    '/pages/problem/list/index',
    '/pages/opinion/collect',
    '/pages/opinion/summary/index',
    '/pages/news/index',
    '/pages/notice/index',
    '/pages/meet/apply',
    '/pages/meet/info',
  ]
  if (!whiteList.includes(path)) {
    uni.showToast({
      title: '敬请期待',
      icon: 'none',
    })
    return
  }
  /** HACK: 临时解决, 后续不做拦截时把any去掉 */
  useNavigate(path as any)
}

function getMenuIcon(path: MenuPaths) {
  const name = path.split('/').slice(2).join('-')
  return modules[name]
}
</script>

<template>
  <PageContainer>
    <view v-for="menu in menus" :key="menu.name">
      <view class="menu-title">{{ menu.name }}</view>
      <view class="menu-children flex flex-wrap space-x-30rpx mt-20rpx">
        <view
          class="w-177rpx h-160rpx flex flex-col items-center justify-center bg-#FCF7F7"
          v-for="child in menu.children"
          :key="child.name"
          @click="handleClick(child.path)"
        >
          <image :src="getMenuIcon(child.path)" class="w-46rpx h-46rpx" />
          <view class="text-#383838 text-28rpx mt-15rpx">{{ child.name }}</view>
        </view>
      </view>
    </view>
  </PageContainer>
</template>

<style scoped></style>
