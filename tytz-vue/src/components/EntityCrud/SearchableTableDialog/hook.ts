import { computed, ref } from "vue";

import type { UseSearchableTableOptions } from "../SearchableTable/hook";

export interface UseSearchableTableDialogOptions<T> extends UseSearchableTableOptions<T> {
  /** 弹窗宽度 */
  width?: string | number;
  /** 标题 */
  title?: string;
  /** 弹窗参数 */
  params?: any;
}

/**
 * EditableForm 组件的 Hook
 */
export function useSearchableTableDialog<T>(options: UseSearchableTableDialogOptions<T>) {
  const visible = ref(false);
  const params = ref(options.params);

  const props = computed(() => ({
    visible: visible.value,
    width: options.width,
    title: options.title,
    params: params.value,
    onCancel: () => {
      visible.value = false;
    },
    ...options,
  }));

  const show = (params: any): Promise<void> => {
    params.value = params;
    visible.value = true;
    return Promise.resolve();
  };

  return {
    props,
    show,
  };
}
