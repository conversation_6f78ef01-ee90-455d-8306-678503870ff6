package com.youlai.boot.modules.activity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youlai.boot.modules.activity.model.entity.Activity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.activity.model.query.ActivityQuery;
import com.youlai.boot.modules.activity.model.vo.ActivityVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 活动管理Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper
public interface ActivityMapper extends BaseMapper<Activity> {

    /**
     * 获取活动管理分页数据
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return 活动分页数据
     */
    Page<ActivityVO> getActivityPage(Page<ActivityVO> page, ActivityQuery queryParams);
} 