<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.work.mapper.KeyWorkMapper">

    <!-- 年度重点工作分页列表 -->
    <select id="getKeyWorkPage" resultType="com.youlai.boot.modules.work.model.vo.KeyWorkVO">
        SELECT
            id,
            year,
            work_name,
            work_type,
            department,
            participants,
            start_time,
            end_time,
            work_content,
            attachments,
            create_time
        FROM tsz_key_work
        <where>
            is_deleted = 0
            <if test="queryParams.year != null">
                AND year = #{queryParams.year}
            </if>
            <if test="queryParams.workName != null and queryParams.workName != ''">
                AND work_name LIKE CONCAT('%', #{queryParams.workName}, '%')
            </if>
            <if test="queryParams.workType != null and queryParams.workType != ''">
                AND work_type = #{queryParams.workType}
            </if>
<!--            <if test="queryParams.department != null and queryParams.department != ''">-->
<!--                AND department LIKE CONCAT('%', #{queryParams.department}, '%')-->
<!--            </if>-->
            <if test="queryParams.startTime != null">
                AND start_time &gt;= #{queryParams.startTime}
            </if>
            <if test="queryParams.endTime != null">
                AND end_time &lt;= #{queryParams.endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper> 