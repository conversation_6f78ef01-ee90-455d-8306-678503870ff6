package com.youlai.boot.modules.welfare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.welfare.model.form.WelfareForm;
import com.youlai.boot.modules.welfare.model.query.WelfareQuery;
import com.youlai.boot.modules.welfare.model.vo.WelfareVO;
import com.youlai.boot.modules.welfare.service.WelfareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 公益信息前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Tag(name = "公益信息接口")
@RestController
@RequestMapping("/api/v1/welfare")
@RequiredArgsConstructor
public class WelfareController {

    private final WelfareService welfareService;

    @Operation(summary = "公益信息分页列表")
    @GetMapping("/page")
    public PageResult<WelfareVO> getWelfarePage(WelfareQuery queryParams) {
        IPage<WelfareVO> result = welfareService.getWelfarePage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增公益活动")
    @PostMapping
    public Result<Void> saveWelfare(@RequestBody @Valid WelfareForm formData) {
        boolean result = welfareService.saveWelfare(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取公益活动表单数据")
    @GetMapping("/{id}/form")
    public Result<WelfareForm> getWelfareForm(
        @Parameter(description = "公益活动ID") @PathVariable Long id
    ) {
        WelfareForm formData = welfareService.getWelfareFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改公益活动")
    @PutMapping(value = "/{id}")
    public Result<Void> updateWelfare(
            @Parameter(description = "公益活动ID") @PathVariable Long id,
            @RequestBody @Validated WelfareForm formData
    ) {
        boolean result = welfareService.updateWelfare(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除公益活动")
    @DeleteMapping("/{ids}")
    public Result<Void> deleteWelfare(
        @Parameter(description = "公益活动ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = welfareService.deleteWelfares(ids);
        return Result.judge(result);
    }
} 