<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '约见详情',
  },
}
</route>
<script lang="ts" setup>
import PageContainer from '@/components/PageContainer/index.vue'
import { getAppointmentForm } from '@/api/meet/index'
import dayjs from 'dayjs'
const props = defineProps<{
  id: string
}>()

const statusEnum = {
  WAIT: {
    label: '待约见',
    color: 'rgba(77, 77, 77, 1)',
  },
  WAIT_START: {
    label: '待开始',
    color: 'rgba(73, 166, 253, 1)',
  },
  MEETING: {
    label: '约见中',
    color: 'rgba(33, 84, 118, 1)',
  },
  END: {
    label: '已结束',
    color: 'rgba(139, 195, 73, 1)',
  },
  REJECT: {
    label: '不予约见',
    color: 'rgba(239, 65, 66, 1)',
  },
}

const detail = ref<Awaited<ReturnType<typeof getAppointmentForm>>>()

onMounted(async () => {
  const res = await getAppointmentForm(props.id)
  detail.value = res
})
</script>
<template>
  <PageContainer theme="page">
    <view class="p-4 bg-white">
      <view class="flex mb-3 items-center">
        <text class="text-sm text-gray-500 w-180rpx flex-shrink-0">约见人名称：</text>
        <text class="text-sm text-gray-800">{{ detail?.appointmentName }}</text>
      </view>
      <view class="flex mb-3 items-start">
        <text class="text-sm text-gray-500 w-150rpx flex-shrink-0">所属单位：</text>
        <text class="text-sm text-gray-800">{{ detail?.appointmentUnit }}</text>
      </view>
      <view class="flex mb-3 items-start">
        <text class="text-sm text-gray-500 w-150rpx flex-shrink-0">联系方式：</text>
        <view class="text-sm text-gray-800">
          {{ detail?.appointmentContact }}
        </view>
      </view>
      <view class="flex mb-3 items-start">
        <text class="text-sm text-gray-500 w-150rpx flex-shrink-0">约见部委：</text>
        <view class="text-sm text-gray-800">
          {{ detail?.appointmentDepartment }}
        </view>
      </view>
      <view class="flex mb-3 items-start">
        <text class="text-sm text-gray-500 w-150rpx flex-shrink-0">约见时间：</text>
        <view class="text-sm text-gray-800">
          {{ dayjs(detail?.appointmentStartTime).format('YYYY-MM-DD HH:mm') }} 至
          {{ dayjs(detail?.appointmentEndTime).format('YYYY-MM-DD HH:mm') }}
        </view>
      </view>
      <view class="mb-3 items-start">
        <text class="text-sm text-gray-500 w-30 flex-shrink-0">约见原因</text>
        <view class="text-sm text-gray-800 leading-relaxed mt-2">
          {{ detail?.appointmentReason }}
        </view>
      </view>
      <view class="mt-6 pt-4 border-t border-gray-200">
        <view class="flex mb-3 items-center">
          <text class="text-sm text-gray-500 w-150rpx flex-shrink-0">约见状态：</text>
          <text class="text-sm" :style="{ color: statusEnum[detail?.appointmentStatus]?.color }">
            {{ statusEnum[detail?.appointmentStatus]?.label || '-' }}
          </text>
        </view>
      </view>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped>
//
</style>
