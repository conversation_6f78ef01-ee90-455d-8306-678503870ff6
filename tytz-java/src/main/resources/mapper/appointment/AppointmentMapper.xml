<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.appointment.mapper.AppointmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youlai.boot.modules.appointment.model.entity.Appointment">
        <id column="id" property="id"/>
        <result column="appointment_name" property="appointmentName"/>
        <result column="appointment_unit" property="appointmentUnit"/>
        <result column="appointment_contact" property="appointmentContact"/>
        <result column="appointment_department" property="appointmentDepartment"/>
        <result column="appointment_reason" property="appointmentReason"/>
        <result column="appointment_start_time" property="appointmentStartTime"/>
        <result column="appointment_end_time" property="appointmentEndTime"/>
        <result column="handle_status" property="handleStatus"/>
        <result column="handle_comment" property="handleComment"/>
        <result column="feedback" property="feedback"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 在这里可以添加自定义的SQL查询 -->
    
</mapper> 