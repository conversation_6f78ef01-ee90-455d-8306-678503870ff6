package com.youlai.boot.modules.notice.service.impl;

import cn.hutool.json.JSONUtil;

import com.youlai.boot.common.enums.PublishStatusEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.system.mapper.UserMapper;
import com.youlai.boot.system.model.bo.UserBO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.modules.notice.mapper.TyTzNoticeMapper;
import com.youlai.boot.modules.notice.service.TyTzNoticeService;
import com.youlai.boot.modules.notice.model.entity.TyTzNotice;
import com.youlai.boot.modules.notice.model.form.TyTzNoticeForm;
import com.youlai.boot.modules.notice.model.form.TyTzNoticeForm.FileDTO;
import com.youlai.boot.modules.notice.model.query.TyTzNoticeQuery;
import com.youlai.boot.modules.notice.model.vo.TyTzNoticeVO;
import com.youlai.boot.modules.notice.converter.TyTzNoticeConverter;

import java.util.Arrays;
import java.util.List;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;


/**
 * 通知公告服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Service
@RequiredArgsConstructor
public class TyTzTzNoticeServiceImpl extends ServiceImpl<TyTzNoticeMapper, TyTzNotice> implements TyTzNoticeService {

    private final TyTzNoticeConverter tyNoticeConverter;
    private final UserMapper userMapper;

    /**
    * 获取通知公告分页列表
    *
    * @param queryParams 查询参数
    * @return {@link IPage< TyTzNoticeVO >} 通知公告分页列表
    */
    @Override
    public IPage<TyTzNoticeVO> getNoticePage(TyTzNoticeQuery queryParams) {
        Page<TyTzNoticeVO> pageVO = this.baseMapper.getNoticePage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );
        return pageVO;
    }
    
    /**
     * 获取通知公告表单数据
     *
     * @param id 通知公告ID
     * @return
     */
    @Override
    public TyTzNoticeForm getNoticeFormData(Long id) {
        TyTzNotice entity = this.getById(id);
        List<FileDTO> list = JSONUtil.toList(entity.getAttachments(), FileDTO.class);
        TyTzNoticeForm form = tyNoticeConverter.toForm(entity);
        form.setAttachments(list);
        // 根据createBy查询用户昵称
        if (entity.getCreateBy() != null) {
            UserBO userBO = userMapper.getUserProfile(entity.getCreateBy());
            if (userBO != null) {
                form.setCreateBy(userBO.getNickname());
            }
        }
        return form;
    }
    
    /**
     * 新增通知公告
     *
     * @param formData 通知公告表单对象
     * @return
     */
    @Override
    public boolean saveNotice(TyTzNoticeForm formData) {
        TyTzNotice entity = tyNoticeConverter.toEntity(formData);

//        for (FileDTO dto : formData.getAttachments()) {
//            dto.setUrl(StringEscapeUtils.unescapeHtml4(dto.getUrl()));
//        }

        if(formData.getPublishStatus() == null) {
            entity.setPublishStatus(PublishStatusEnum.WAIT);
        }
        
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setCreateBy(SecurityUtils.getUserId());
        return this.save(entity);
    }
    
    /**
     * 更新通知公告
     *
     * @param id   通知公告ID
     * @param formData 通知公告表单对象
     * @return
     */
    @Override
    public boolean updateNotice(Long id, TyTzNoticeForm formData) {
        TyTzNotice entity = tyNoticeConverter.toEntity(formData);
        entity.setAttachments(JSONUtil.toJsonStr(formData.getAttachments()));
        entity.setUpdateBy(SecurityUtils.getUserId());
        return this.updateById(entity);
    }
    
    /**
     * 删除通知公告
     *
     * @param ids 通知公告ID，多个以英文逗号(,)分割
     * @return
     */
    @Override
    public boolean deleteNotices(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的通知公告数据为空");
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();
        return this.removeByIds(idList);
    }

}
