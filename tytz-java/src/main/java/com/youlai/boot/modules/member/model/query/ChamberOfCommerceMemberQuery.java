package com.youlai.boot.modules.member.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商会会员查询对象
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@EqualsAndHashCode
@Schema(description = "商会会员查询对象")
@Data
public class ChamberOfCommerceMemberQuery {
    /**
     * 商会编号列表
     */
    @Schema(description = "商会编号")
    private String[] chamberOfCommerceCodes;

    @Schema(description = "商会会员名称")
    private String memberName;

}
