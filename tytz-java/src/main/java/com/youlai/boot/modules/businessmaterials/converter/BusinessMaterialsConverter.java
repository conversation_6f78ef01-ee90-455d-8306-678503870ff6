package com.youlai.boot.modules.businessmaterials.converter;

import org.mapstruct.Mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.businessmaterials.model.entity.BusinessMaterials;
import com.youlai.boot.modules.businessmaterials.model.form.BusinessMaterialsForm;
import org.mapstruct.Mapping;

/**
 * 营商材料对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper(componentModel = "spring")
public interface BusinessMaterialsConverter{
    @Mapping(target = "attachments", ignore = true)
    BusinessMaterialsForm toForm(BusinessMaterials entity);

    @Mapping(target = "attachments", ignore = true)
    BusinessMaterials toEntity(BusinessMaterialsForm formData);
}