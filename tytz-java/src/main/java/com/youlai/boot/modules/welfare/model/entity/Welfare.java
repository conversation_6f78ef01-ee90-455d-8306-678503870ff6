package com.youlai.boot.modules.welfare.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 公益信息实体对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@TableName("tsz_welfare")
public class Welfare extends BaseEntity {

    /**
     * 公益活动名称
     */
    private String title;
    
    /**
     * 主办单位
     */
    private String department;
    
    /**
     * 参与人员JSON数据
     */
    private String participants;
    
    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 活动内容
     */
    private String content;
    
    /**
     * 附件JSON数据，包含文件名、路径等信息
     */
    private String attachments;
    
    /**
     * 创建人ID
     */
    private Long createBy;
    
    /**
     * 更新人ID
     */
    private Long updateBy;
    
    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
} 