package com.youlai.boot.modules.work.model.form;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import java.util.List;

import com.youlai.boot.common.enums.WorkTypeEnum;

import jakarta.validation.constraints.*;

/**
 * 年度重点工作表单对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Schema(description = "年度重点工作表单对象")
public class KeyWorkForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "年度")
    private Integer year;

    @Schema(description = "工作名称")
    @NotBlank(message = "工作名称不能为空")
    @Size(max=100, message="工作名称长度不能超过100个字符")
    private String workName;

    @Schema(description = "工作类型")
    @NotNull(message = "工作类型不能为空")
    private WorkTypeEnum workType;

//    @Schema(description = "负责部门")
//    @NotBlank(message = "负责部门不能为空")
//    @Size(max=50, message="负责部门长度不能超过50个字符")
//    private String department;

    @Schema(description = "负责人员")
    @NotNull(message = "负责人员不能为空")
    private List<ParticipantDTO> participants;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "工作内容")
    private String workContent;

    @Schema(description = "附件")
    private List<FileDTO> attachments;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ParticipantDTO {

        @Schema(description = "用户ID")
        private Long id;

        @Schema(description = "用户名")
        private String businessName;

        @Schema(description = "用户信息")
        private String businessMember;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileDTO {
        @Schema(description = "文件url")
        String url;

        @Schema(description = "文件名称")
        String name;
    }
} 