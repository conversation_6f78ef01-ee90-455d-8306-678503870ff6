<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import AppointmentDetail from "./components/AppointmentDetail.vue";
import { useEditableDialog } from "@/components/EntityCrud/EditableDialog/hook";
import EditableDialog from "@/components/EditableForm/index.vue";
import { statusEnum, statusStyleEnum } from "./enum";
import { downloadBlobFile } from "@/utils/file";
import {
  appointmentsPageVO,
  appointmentPageApi,
  handleAppointmentApi,
  feedbackApi,
  feedbackExportApi,
} from "@/api/appointment/index";
import dayjs from "dayjs";
import { ElMessageBox, ElMessage } from "element-plus";
import { useUserStore } from "@/store/modules/user";

const userStore = useUserStore();
const entityCrudRef = ref<any>(null);
const updateFormRef = ref<any>(null);
const showFeedbackDialog = async (data: appointmentsPageVO) => {
  data.feedback = "";
  await showUpdateDialog(data);
};
const { formProps: updateFormProps, showDialog: showUpdateDialog } = useEditableDialog({
  title: "约见反馈",
  formItems: {
    id: {
      type: "id",
      label: "id",
    },
    feedback: {
      type: "editor",
      label: "",
    },
  },
  submitRequest: async (data: any) => {
    const id = data.id;
    delete data.id;
    return feedbackApi(id, data)
      .then(() => {
        entityCrudRef.value?.handleRefresh();
      })
      .catch((err) => {
        console.log(err);
      });
  },

  width: "800px",
});

const config: EntityCrudProps<appointmentsPageVO> = {
  entityName: "appo_transact",
  displayName: "约见办理",
  hasIndexColumn: true,
  hasSelectionColumn: true,
  operations: [
    {
      type: "button",
      label: "导出",
      colorize: "primary",
      actionService: async () => {
        const { getCurrentFormData, selectedRows } = entityCrudRef.value;
        const formData = getCurrentFormData();
        const idsList = selectedRows.map((row: any) => String(row.id)).join(",");
        const exportParams = {
          ...(idsList ? { ids: idsList } : {}),
          appointmentName: formData.appointmentName,
          appointmentStatus: formData.appointmentStatus,
          startTimeBegin: formData.startTimeBegin,
          startTimeEnd: formData.startTimeEnd,
        };
        try {
          await ElMessageBox.confirm("确认要导出数据吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });
          const response = await feedbackExportApi(exportParams);
          if (response) {
            const {
              data,
              fileName = "约见办理数据.xlsx",
              fileType = "application/vnd.ms-excel;charset=utf-8",
            } = response as any;
            const decodedFileName = decodeURIComponent(fileName);
            downloadBlobFile(data, decodedFileName, fileType);
          }
        } catch {
          ElMessage.info("已取消导出");
        }
      },
    },
  ],
  filterFormItems: {
    appointmentName: {
      type: "input",
      label: "约见人名称",
    },
    appointmentStatus: {
      type: "select",
      label: "约见状态",
      options: getEnumOptions(statusEnum),
    },
    createTime: {
      type: "date-range",
      label: "约见时间",
      dateRangeFields: ["startTimeBegin", "startTimeEnd"],
    },
  },
  tableColumns: {
    appointmentName: "约见人名称",
    appointmentUnit: "所属单位",
    appointmentDepartment: "被约见部委",
    appointmentStartTime: "约见开始时间",
    appointmentEndTime: "约见结束时间",
    appointmentStatus: "约见状态",
    operations: {
      label: "操作",
    },
  },
  rowOperations: [
    {
      label: "约见",
      type: "link",
      displayIndex: -1,
      canDisplay: (row) => {
        return row.appointmentStatus == statusStyleEnum.WAIT.value;
      },
      actionService: async (row: appointmentsPageVO) => {
        currentAppointment.value = row;
        showDetailDialog.value = true;
      },
    },
    {
      label: "查看",
      type: "link",
      displayIndex: 1,
      canDisplay: (row) => {
        return row.handleStatus != statusStyleEnum.WAIT.value;
      },
      actionService: async (row: appointmentsPageVO) => {
        currentAppointment.value = row;
        showDetailDialog.value = true;
      },
    },
    {
      label: "反馈",
      type: "link",
      displayIndex: 2,
      canDisplay: (row) => {
        return row.appointmentStatus == statusStyleEnum.END.value && !row.feedback;
      },
      actionService: async (row) => {
        await showFeedbackDialog(row);
      },
    },
  ],

  listFetchService: async (params) => {
    return appointmentPageApi(params);
  },
};

const entityCrudProps = defineEntityCrud(config);

const showDetailDialog = ref(false);
const currentAppointment = ref<appointmentsPageVO>();

const handleDetailConfirm = async (formData: any) => {
  try {
    const id = formData.id;
    delete formData.id;
    await handleAppointmentApi(id, formData);
    showDetailDialog.value = false;
    entityCrudRef.value?.handleRefresh();
    userStore.fetchAppointmentCount();
  } catch (err: any) {
    if (err.msg) ElMessage.error(err.msg);
  } finally {
    entityCrudRef.value?.handleRefresh();
  }
};
</script>

<template>
  <div>
    <div class="entity-crud-wrapper">
      <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
        <template #tableAppointmentStartTime="{ row }">
          <div>{{ dayjs(row.appointmentStartTime).format("YYYY-MM-DD HH:mm") }}</div>
        </template>
        <template #tableAppointmentEndTime="{ row }">
          <div>{{ dayjs(row.appointmentEndTime).format("YYYY-MM-DD HH:mm") }}</div>
        </template>
        <template #tableAppointmentStatus="{ row }: { row: appointmentsPageVO }">
          <div
            class="flex"
            :style="{
              color: statusStyleEnum[row.appointmentStatus as keyof typeof statusEnum]?.color,
            }"
          >
            <div
              class="pointer"
              :style="{
                backgroundColor:
                  statusStyleEnum[row.appointmentStatus as keyof typeof statusEnum]?.color,
              }"
            ></div>
            {{ statusEnum[row.appointmentStatus as keyof typeof statusEnum] }}
          </div>
        </template>
      </EntityCrud>
    </div>
    <EditableDialog v-bind="updateFormProps" ref="updateFormRef"></EditableDialog>
    <AppointmentDetail
      v-model:visible="showDetailDialog"
      :data="currentAppointment"
      @confirm="handleDetailConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.pointer {
  width: 8px;
  height: 8px;
  margin-top: 7px;
  margin-right: 4px;
  border-radius: 50%;
}
</style>
./enum
