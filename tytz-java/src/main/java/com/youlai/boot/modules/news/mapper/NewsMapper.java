package com.youlai.boot.modules.news.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.news.model.entity.News;
import com.youlai.boot.modules.news.model.query.NewsQuery;
import com.youlai.boot.modules.news.model.vo.NewsVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 新闻资讯Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Mapper
public interface NewsMapper extends BaseMapper<News> {

    /**
     * 获取新闻资讯分页列表
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return 分页列表
     */
    Page<NewsVO> getNewsPage(Page<NewsVO> page, NewsQuery queryParams);
}