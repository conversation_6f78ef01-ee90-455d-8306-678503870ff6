package com.youlai.boot.modules.problem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.problem.model.entity.Problem;
import com.youlai.boot.modules.problem.model.query.ProblemQuery;
import com.youlai.boot.modules.problem.model.vo.ProblemVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 营商环境问题Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Mapper
public interface ProblemMapper extends BaseMapper<Problem> {

    /**
     * 获取营商环境问题分页列表
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<ProblemVO> getProblemPage(Page<ProblemVO> page, ProblemQuery queryParams);
} 