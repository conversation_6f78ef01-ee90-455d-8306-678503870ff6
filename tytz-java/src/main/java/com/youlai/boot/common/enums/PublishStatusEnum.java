package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 数据权限枚举
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Getter
public enum PublishStatusEnum implements IBaseEnum<String> {


    WAIT("WAIT", "待审核"),
    PUSH("PUSH", "审核通过"),
    CANCEL("CANCEL", "审核驳回");

    private final String value;

    private final String label;

    PublishStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
