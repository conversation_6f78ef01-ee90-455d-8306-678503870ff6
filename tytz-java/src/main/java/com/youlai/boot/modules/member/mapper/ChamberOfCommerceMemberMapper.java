package com.youlai.boot.modules.member.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.youlai.boot.modules.member.model.query.ChamberOfCommerceMemberQuery;
import com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO;

/**
 * 商会成员管理Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Mapper
public interface ChamberOfCommerceMemberMapper {
    /**
     * 获取商会会员列表
     *
     * @param queryParams 查询参数
     * @return 商会会员列表
     */
    List<ChamberOfCommerceMemberVO> getChamberOfCommerceMemberList(
            @Param("queryParams") ChamberOfCommerceMemberQuery queryParams);
}
