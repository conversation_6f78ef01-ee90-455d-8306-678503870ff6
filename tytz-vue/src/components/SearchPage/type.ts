import type { CustomComponentProps } from "@/components/CustomComponent/type";
import type { FormItemsUnionTypeList } from "@/components/Form/type";
import type { TableColumnType } from "@/components/Table/type";

import type { RESULT_LIST } from "./config";

export interface SearchPageProps<R extends object, T extends R> {
  service: (...args: any[]) => Promise<{
    [RESULT_LIST.LIST]: R[];
    [RESULT_LIST.TOTAL]: number;
  }>;
  formItems?: FormItemsUnionTypeList;
  operations?: (CustomComponentProps & {
    onClick?: () => void;
  })[];
  tableColumns?: TableColumnType<T>;
  /**
   * 是否在页面激活时刷新请求
   * @default false
   * 实验性功能，可能会有变动
   * 如果此值设置为 true 请确保被 `keep-alive` 组件包裹
   */
  activeRefresh?: boolean;
}
