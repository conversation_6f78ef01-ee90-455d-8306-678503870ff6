import dayjs from "dayjs";

import type { FormItemUnionType } from "../EditableForm/type";
import type { DisplayableItem, EditableFormUnionType } from "./type";

export const initRules = (item: EditableFormUnionType) => {
  return [
    ...(item.required
      ? [
          {
            required: true,
            message: item.requiredTip ?? `请输入${item.label}`,
            trigger: "blur",
          },
          {
            validator: (rule: any, value: string, callback: (error?: Error) => void) => {
              if (value === undefined) {
                callback(new Error(`${item.label}不能全是空格`));
              }
              callback();
            },
            trigger: "blur",
          },
        ]
      : []),
    ...(item.type === "mobile"
      ? [
          {
            type: "regexp",
            pattern: /^1\d{10}$/,
            trigger: "blur",
            message: "请输入正确的手机号",
          },
        ]
      : []),
    ...(item.type === "tel"
      ? [
          {
            type: "regexp",
            pattern: /^[\d-]{6,}$/,
            trigger: "blur",
            message: "请输入正确的电话号码",
          },
        ]
      : []),
  ];
};

export const mapEntityFormItemToEditableFormItem = (
  key: string,
  item: EditableFormUnionType | string
): FormItemUnionType => {
  const isMapper: Record<string, FormItemUnionType["is"]> = {
    select: "select",
    radio: "radio",
    switch: "switch",
    editor: "editor",
    "single-document": "document-upload",
    "multiple-document": "document-upload",
    "select-remote": "select-remote",
    "date-picker": "date-picker",
    id: "input",
  };
  if (typeof item === "string") {
    return {
      is: "input" as const,
      formItem: {
        prop: key,
        label: item,
      },
    };
  }
  const mappedIs = isMapper[item.type as keyof typeof isMapper] || "input";
  if (!Object.values(isMapper).includes(mappedIs as any)) {
    throw new Error(`Unsupported type: ${item.type}`);
  }

  return {
    is: mappedIs as FormItemUnionType["is"],
    width: item.width,
    slotOnFormItem: item.type === "slot" && item.slotOnFormItem,
    formItem: {
      prop: key,
      label: item.label || "",
      rules: initRules(item),
    },
    props: {
      ...(item.props || {}),
      ...(item.type === "number" ? { type: "number" } : {}),
      ...(item.type === "non-negative-number" ? { type: "number", min: 0, step: 1 } : {}),
      ...(item.type === "select-remote"
        ? {
            clearable: true,
            remoteMethods: item.remoteFetchService,
          }
        : {}),
      ...(item.type === "select" ? { options: item.options } : {}),
      ...(item.type === "radio" && "options" in item
        ? {
            options: item.options,
            initialValue: item.initialValue,
          }
        : {}),
      ...(item.type === "textarea" ? { type: "textarea", rows: 3 } : {}),
      ...(item.type === "single-document"
        ? {
            maxFileCount: 1,
            acceptType: item.acceptType || ".pdf,.xls,.doc",
            tip: item.tip || "",
          }
        : {}),
      ...(item.type === "multiple-document"
        ? {
            maxFileCount: item.maxFileCount || 5,
            acceptType: item.acceptType || "",
            tip: item.tip || "",
          }
        : {}),
      ...(item.type === "single-image"
        ? {
            maxFileCount: 1,
            tip: item.tip || "",
          }
        : {}),
      ...(item.type === "multiple-image"
        ? { maxFileCount: item.maxFileCount || 5, tip: item.tip || "" }
        : {}),
      ...(item.type === "switch"
        ? {
            activeText: item.activeText,
            inactiveText: item.inactiveText,
            activeValue: item.activeValue,
            inactiveValue: item.inactiveValue,
          }
        : {}),
      ...(item.type === "mobile" ? { maxlength: 11 } : {}),
      ...(item.type === "tel" ? { maxlength: 15, style: "width: 200px" } : {}),
      ...(item.type === "person-name" ? { maxlength: 8, style: "width: 100px" } : {}),
      ...(item.type === "date-picker"
        ? { style: { width: "100%" }, type: "daterange", search: false }
        : {}),
      placeholder: item.placeholder ?? `请输入${item.label}`,
    },
    hidden: item.type === "id",
  } as FormItemUnionType;
};
export function mapEntityFormItemToDisplayableItem(
  key: string,
  item: EditableFormUnionType | string
): DisplayableItem {
  const typeMapper = {
    editor: "rich-text",
    "single-document": "documents",
    "multiple-document": "documents",
    "single-image": "images",
    "multiple-image": "images",
    radio: "radio",
  } as const;

  if (typeof item === "string") {
    return {
      prop: key,
      label: item,
      type: "text",
      placeholder: "",
    };
  }

  return {
    prop: key,
    label: item.label ?? "",
    type: typeMapper[item.type as keyof typeof typeMapper] || "text",
    placeholder: item.placeholder ?? "",
  };
}

export function getEnumOptions(enumObj: Record<string, string>) {
  return Object.keys(enumObj).map((key) => ({
    label: enumObj[key],
    value: key,
  }));
}

export function formatDate(date: Date) {
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
}
