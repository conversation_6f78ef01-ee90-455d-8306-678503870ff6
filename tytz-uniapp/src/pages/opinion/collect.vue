<route lang="json5">
{
  style: {
    navigationBarTitleText: '意见征集',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { useNewUserStoreHook } from '@/store/modules/user'
import { collectOpinion } from '@/api/opinion/collect'
defineOptions({
  name: 'OpinionCollect',
})
const userStore = useNewUserStoreHook()

const formDataList = [
  {
    title: '商会成员名称',
    prop: 'memberName',
    type: 'input',
    readonly: true,
    required: true,
    value: userStore.userInfo.nickname,
  },
  {
    title: '所属单位',
    prop: 'department',
    type: 'input',
    readonly: true,
    required: false,
    value: userStore.userInfo.company,
  },
  {
    title: '联系方式',
    prop: 'contact',
    type: 'input',
    readonly: true,
    required: true,
    value: userStore.userInfo.mobile,
    rule: {
      title: '请输入正确的手机号码',
      visible(value: string) {
        // 检验手机格式
        return /^1[3-9]\d{9}$/.test(value)
      },
    },
  },
  {
    title: '意见描述',
    prop: 'content',
    type: 'textarea',
    required: true,
  },
]

const formData = reactive(
  Object.fromEntries(formDataList.map((item) => [item.prop, item.value || ''])),
)
console.log('formData', formData)

const submit = async () => {
  for (const key of formDataList) {
    if (!formData[key.prop]) {
      uni.showToast({
        title: '请输入' + key.title,
        icon: 'none',
      })
      return
    }
  }
  for (const key of formDataList) {
    if (key.rule) {
      if (!key.rule.visible(formData[key.prop])) {
        uni.showToast({
          title: key.rule.title,
          icon: 'none',
          duration: 2000,
        })
        return
      }
    }
  }
  const parmas = {
    ...formData,
    memberRole: userStore.role.id,
  }
  await collectOpinion(parmas)
  uni.navigateBack().then(() => {
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })
  })
}
</script>

<template>
  <PageContainer theme="page">
    <view class="px-4 py-3">
      <view class="mb-4" v-for="item in formDataList" :key="item.prop">
        <view class="text-[14px] text-[#333] mb-2">
          {{ item.title }}
          <text class="text-#F51414" v-if="item.required">*</text>
        </view>
        <input
          v-if="item.type === 'input'"
          v-model="formData[item.prop]"
          class="w-full h-[44px] rounded border border-solid border-#A6A6A6 box-border px-20rpx"
          :placeholder="'请输入' + item.title"
          placeholder-class="text-gray-400"
          :disabled="item.readonly"
        />
        <textarea
          v-else-if="item.type === 'textarea'"
          v-model="formData[item.prop]"
          class="w-full min-h-[200px] rounded border border-solid border-#A6A6A6 box-border p-20rpx"
          :placeholder="'请输入' + item.title"
          placeholder-class="text-gray-400!"
          :maxlength="500"
          :disabled="item.readonly"
        />
      </view>

      <!-- 提交按钮 -->
      <view
        class="w-full h-[44px] bg-[#cc3333] text-white rounded text-center leading-[44px] mt-8"
        @click="submit"
      >
        提交
      </view>
    </view>
  </PageContainer>
</template>

<style scoped></style>
