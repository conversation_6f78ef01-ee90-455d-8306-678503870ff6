import { useEditableForm } from "@/components/EditableForm/hook";

import type { EditableItem } from "../type";
import { mapEntityFormItemToEditableFormItem } from "../util";

export interface UseDialogOptions<TData, TFetchedData> {
  /** 标题 */
  title: string;
  /** 表单项配置 */
  formItems: EditableItem<TData | TFetchedData | any>;
  /** 初始数据服务 */
  initialDataService?: (originalData: TData) => Promise<TFetchedData>;
  /** 新增请求 */
  submitRequest?: (data: TData | TFetchedData) => Promise<any>;
  /** 成功回调 */
  onSuccess?: (data?: TData | TFetchedData) => void;
  /** 表单宽度 */
  width?: string | number;
}

export function useCreatableDialog<T>(options: UseDialogOptions<T, T>) {
  const formItems = options.formItems || {};

  const { formProps, newEditable } = useEditableForm({
    customTitle: options.title,
    addRequest: options.submitRequest as (data: Record<string, any>) => Promise<any>,
    onSuccess: options.onSuccess,
    width: options.width,
    formItems: Object.keys(formItems)
      .filter((key) => typeof formItems[key] === "object" && formItems[key]?.type !== "id")
      .map((key) => {
        const formItem = formItems[key];
        return formItem ? mapEntityFormItemToEditableFormItem(key, formItem) : null;
      })
      .filter((item) => item !== null),
  });

  return {
    formProps,
    showDialog: newEditable,
  };
}

export function useEditableDialog<TUpdate, TUpdateableData>(
  options: UseDialogOptions<TUpdate, TUpdateableData>
) {
  const formItems = options.formItems || {};

  const { formProps, editEditable } = useEditableForm({
    customTitle: options.title,
    initialDataService: options.initialDataService as (
      originalData: Record<string, any>
    ) => Promise<any>,
    editRequest: options.submitRequest as (data: Record<string, any>) => Promise<any>,
    onSuccess: options.onSuccess,
    width: options.width ?? "800px",
    formItems: Object.keys(formItems)
      .map((key) =>
        formItems[key] ? mapEntityFormItemToEditableFormItem(key, formItems[key]) : null
      )
      .filter((item) => item !== null),
  });

  return {
    formProps,
    showDialog: editEditable,
  };
}
