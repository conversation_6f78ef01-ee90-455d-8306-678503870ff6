<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.member.mapper.ChamberOfCommerceMemberMapper">
    <select id="getChamberOfCommerceMemberList" resultType="com.youlai.boot.modules.member.model.vo.ChamberOfCommerceMemberVO">
        SELECT
        	su.id,
        	su.username,
        	su.nickname,
        	su.mobile,
        	su.gender,
        	su.avatar,
        	su.email,
        	su.status,
        	su.company
        FROM
        	sys_user su
        WHERE
        	su.STATUS = 1
        <if test="queryParams.memberName != null and queryParams.memberName != ''">
            AND username LIKE CONCAT('%', #{queryParams.memberName}, '%')
        </if>
        <if test="queryParams.chamberOfCommerceCodes != null and queryParams.chamberOfCommerceCodes.length > 0">
            AND su.id IN (
                SELECT sur.user_id 
                    FROM sys_role sr 
                    LEFT JOIN sys_user_role sur ON sur.role_id = sr.id 
                WHERE sr.CODE IN
            <foreach collection="queryParams.chamberOfCommerceCodes" item="code" open="(" separator="," close=")">
                    #{code}
            </foreach>
            )
        </if>
        ORDER BY
	    	su.create_time DESC
    </select>
</mapper>