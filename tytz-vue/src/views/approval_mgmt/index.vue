<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import dayjs from "dayjs";
import {
  leaderInstructPageApi,
  LeaderInstructPageVO,
  leaderInstructApi,
  LeaderInstructVO,
} from "@/api/biz_env/approval";
const statusEnum = {
  WAIT: "未批示",
  PASS: "已批示",
} as const;

const statusStyleEnum = {
  WAIT: {
    label: "未批示",
    color: "rgba(77, 77, 77, 1)",
    value: "WAIT",
  },
  PASS: {
    label: "已批示",
    color: "rgba(139, 195, 73, 1)",
    value: "PASS",
  },
} as Record<string, { label: string; color: string; value: string }>;

const approvalEnum = {
  Q: "区领导批示",
  S: "市领导批示",
} as { [key: string]: string };

const entityCrudRef = ref<any>(null);
const dialogVisible = ref(false);
const detailData = ref();

const config: EntityCrudProps<LeaderInstructPageVO> = {
  entityName: "response",
  displayName: "领导批示",
  hasIndexColumn: true,
  filterFormItems: {
    memberName: {
      type: "input",
      label: "商户会员名称",
    },
    instructionStatus: {
      type: "select",
      label: "批示状态",
      options: getEnumOptions(statusEnum),
    },
    createTime: {
      type: "date-range",
      label: "批示时间",
      dateRangeFields: ["createTimeStart", "createTimeEnd"],
    },
  },
  tableColumns: {
    memberName: "商会会员名称",
    department: "所属单位",
    businessType: "营商环境类别",
    title: "标题",
    createTime: "提交时间",
    instructionStatus: "处理状态",
    operations: {
      label: "操作",
      width: 250,
    },
  },
  createFormItems: {
    id: {
      type: "id",
    },
    leaderInstruct: {
      type: "radio",
      label: "领导批示",
      options: getEnumOptions(approvalEnum),
      required: true,
    },
    instructContent: {
      type: "editor",
      label: "批示内容",
    },
  },
  listFetchService: async (params) => {
    return leaderInstructPageApi(params);
  },
  rowOperations: [
    {
      label: "查看",
      type: "link",
      displayIndex: 1,
      canDisplay: (row) => {
        return row.instructionStatus === "PASS";
      },
      actionService: async (row) => {
        dialogVisible.value = true;
        detailData.value = row;
      },
    },
  ],
  canUpdate: (params: LeaderInstructPageVO) => params.instructionStatus === "WAIT",
  updateButtonLabel: "批示",
  useCreateFormItemsAsUpdate: true,
  updateService: (updateRecord: LeaderInstructVO) => {
    return leaderInstructApi(updateRecord);
  },
};
const entityCrudProps = defineEntityCrud(config);
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #tableInstructionStatus="{ row }: { row: LeaderInstructPageVO }">
        <el-tag :type="row.instructionStatus === 'PASS' ? 'success' : 'warning'">
          {{ statusEnum[row.instructionStatus as keyof typeof statusEnum] }}
        </el-tag>
      </template>
      <template #tableCreateTime="{ row }">
        {{ row.createTime ? dayjs(row.createTime).format("YYYY-MM-DD HH:mm") : "-" }}
      </template>
    </EntityCrud>
    <el-dialog :model-value="dialogVisible" title="批示详情" width="30%" center>
      <div class="detail">
        <p>
          领导批示：{{ approvalEnum[detailData?.leaderInstruction as keyof typeof approvalEnum] }}
        </p>
        <p>批示时间：{{ dayjs(detailData?.instructionTime).format("YYYY-MM-DD HH:mm:hh") }}</p>
        <div class="flex">
          批示状态：
          <div
            class="point"
            :style="{
              backgroundColor:
                statusStyleEnum[detailData.instructionStatus as keyof typeof statusEnum]?.color,
            }"
          ></div>
          <div
            :style="{
              color:
                statusStyleEnum[detailData.instructionStatus as keyof typeof statusEnum]?.color,
            }"
          >
            {{ statusEnum[detailData?.instructionStatus as keyof typeof statusEnum] }}
          </div>
        </div>
        <p>批示内容：</p>
        <div v-html="detailData?.instructionContent"></div>
      </div>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.detail {
  padding-left: 30px;
}
.dialog-footer {
  text-align: center;
}
.point {
  width: 8px;
  height: 8px;
  margin-top: 7px;
  margin-right: 4px;
  border-radius: 50%;
}
</style>
