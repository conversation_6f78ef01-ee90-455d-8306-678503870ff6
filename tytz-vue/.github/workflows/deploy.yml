name: Deploy

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 10
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Build project
      run: pnpm run build
          
    - name: Upload build files
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        source: "dist/"
        target: "/opt/tytz/admin-vue/upload"
        
    - name: Finish deployment
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        password: ${{ secrets.SERVER_PASSWORD }}
        script: |
          cd /opt/tytz/admin-vue
          rm -rf dist
          mv upload dist
          cd ..
          docker compose down tytz-admin-vue
          docker compose up -d tytz-admin-vue