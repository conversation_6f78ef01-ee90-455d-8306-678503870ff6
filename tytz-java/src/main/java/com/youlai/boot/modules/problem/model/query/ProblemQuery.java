package com.youlai.boot.modules.problem.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.AdoptStatusEnum;
import com.youlai.boot.common.enums.InstructionStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 营商环境问题分页查询对象
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Schema(description = "营商环境问题查询对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class ProblemQuery extends BasePageQuery {

    @Schema(description = "营商环境标题")
    private String title;

    @Schema(description = "营商环境类别")
    private String businessType;

    @Schema(description = "商会会员名称")
    private String memberName;

    @Schema(description = "所属部门")
    private String department;

    @Schema(description = "采纳状态")
    private AdoptStatusEnum adoptStatus;

    @Schema(description = "批示状态")
    private InstructionStatusEnum instructionStatus;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "问题ID，多个以逗号分隔")
    private String ids;
}