package com.youlai.boot.modules.appointment.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.AppoinitMentEnum;
import com.youlai.boot.common.enums.HandleEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 约见管理查询对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "约见管理查询对象")
@Data
public class AppointmentQuery extends BasePageQuery {

    @Schema(description = "约见人名称")
    private String appointmentName;

    @Schema(description = "所属单位")
    private String appointmentUnit;

    @Schema(description = "被约见部委")
    private String appointmentDepartment;

    @Schema(description = "处理状态（1: 予以约见, 2: 不予约见）")
    private HandleEnum handleStatus;

    @Schema(description = "约见开始时间")
    private String startTimeBegin;

    @Schema(description = "约见结束时间")
    private String startTimeEnd;

    @Schema(description = "约见状态（WAIT: 待处理, WAIT_START: 待开始, MEETING: 进行中, END: 已结束, REJECT: 已拒绝）")
    private AppoinitMentEnum appointmentStatus;

    @Schema(description = "约见ID，多个以逗号分隔")
    private String ids;
}