<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人中心',
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { useNewUserStoreHook } from '@/store/modules/user'
import { TOKEN_TYPE, ACCESS_TOKEN, REFRESH_TOKEN } from '@/const/storage'

defineOptions({
  name: 'Person',
})

const userStore = useNewUserStoreHook()
const show = ref(false)
const selectedRoleId = ref(userStore.role.id)

const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.clearUserInfo()
        uni.removeStorageSync(TOKEN_TYPE)
        uni.removeStorageSync(ACCESS_TOKEN)
        uni.removeStorageSync(REFRESH_TOKEN)
      }
    },
  })
}
const handleClose = () => {
  show.value = false
}
const changeRoles = (e) => {
  const selectedRole = userStore.userInfo.roles.find((role) => role.id === e.value)
  if (selectedRole) {
    userStore.setRole(selectedRole)
    show.value = false
    uni.showToast({
      title: '身份切换成功',
      icon: 'success',
    })
  } else {
    uni.showToast({
      title: '切换失败',
      icon: 'error',
    })
  }
}
</script>

<template>
  <PageContainer theme="person" slot-type="custom">
    <view class="size-full flex flex-col">
      <view class="h-306rpx flex flex-center box-border px-30rpx">
        <image class="w-100rpx h-100rpx" src="@/static/images/person/person.png" />
        <view
          class="flex flex-col flex-1 justify-between h-100rpx ml-20rpx"
          @click="userStore.isLogin || useNavigate('/pages/login/index')"
        >
          <view class="flex">
            <text class="text-white text-34rpx">{{ userStore.userInfo.nickname || '未登录' }}</text>
            <view class="tag text-24rpx text-#DC4F3F ml-20rpx" v-if="userStore.isLogin">
              {{ userStore.role.name }}
            </view>
            <view
              class="text-24rpx text-#FFFFFF ml-auto"
              @click="show = true"
              v-if="userStore.isLogin"
            >
              切换身份
            </view>
            <wd-icon
              name="translate-bold"
              size="16px"
              color="#fff"
              @click="show = true"
              v-if="userStore.isLogin"
            ></wd-icon>
          </view>
          <view class="text-#F4D0D1 text-28rpx" v-if="userStore.isLogin">
            {{ userStore.userInfo.company }}
          </view>
          <view class="text-#F4D0D1 text-28rpx" v-else>点击登录</view>
        </view>
      </view>
      <view v-if="show">
        <wd-popup
          v-model="show"
          custom-style="border-radius:20rpx;padding: 20rpx;width: 500rpx;"
          :closable="true"
          @close="handleClose"
        >
          <view class="text-32rpx text-#333333 text-center">切换身份</view>
          <wd-radio-group
            v-model="selectedRoleId"
            class="custom-radio-group"
            shape="check"
            @change="changeRoles"
          >
            <wd-radio
              v-for="role in userStore.userInfo.roles"
              :key="role.id"
              :value="role.id"
              class="custom-radio"
            >
              {{ role.name }}
            </wd-radio>
          </wd-radio-group>
          <view class="h-40rpx"></view>
        </wd-popup>
      </view>
      <view class="flex-1 bg-#F5F6FA">
        <view class="bg-white cell-group" v-if="userStore.isLogin">
          <wd-cell
            title="修改密码"
            custom-class="cellhhh"
            style="background-color: aqua"
            is-link
            @click="useNavigate('/pages/person/password/index')"
          />
          <wd-cell title="退出登录" custom-class="cellhhh" is-link @click="handleLogout" />
        </view>
      </view>
    </view>
  </PageContainer>
</template>
<style lang="scss">
.tag {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 20rpx;
  line-height: 1.5;
  background: linear-gradient(270deg, #facbc7 0%, #fbeaeb 100%);
  border-radius: 22rpx;
}

.cell-group {
  :deep(.wd-cell.cellhhh) {
    height: 100rpx !important;
    background-color: white !important;

    .wd-cell__title {
      font-size: 32rpx !important;
    }
  }
}
/* 使用 /deep/ 或 ::v-deep 穿透作用域 */
.wd-radio-group {
  :deep(.wd-radio) {
    padding: 10rpx;
  }
  :deep(.wd-radio__label) {
    font-size: 40rpx;
  }
  :deep(.wd-radio.is-checked) {
    background-color: #f0f7ff;
  }
}
</style>
