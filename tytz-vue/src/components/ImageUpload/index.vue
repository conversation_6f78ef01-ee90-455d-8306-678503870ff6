<template>
  <el-upload
    v-model:file-list="fileList"
    :class="{ 'image-upload-limited': props.maxFileCount && props.maxFileCount <= fileList.length }"
    :http-request="uploadFile"
    list-type="picture-card"
    :on-preview="handlePictureCardPreview"
    :before-upload="handleBeforeUpload"
    :before-remove="handleBeforeRemove"
    :accept="acceptType"
    :limit="props.maxFileCount"
    :multiple="false"
    :auto-upload="true"
  >
    <el-icon><Plus /></el-icon>
    <template #tip>
      <section class="tips-wrapper">
        <div v-for="tip in tips" :key="tip" class="upload-tip">
          {{ tip }}
        </div>
      </section>
    </template>
  </el-upload>

  <el-dialog v-model="dialogVisible" :close-on-click-modal="false">
    <div class="image-preview">
      <img :src="dialogImageUrl" alt="Preview Image" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { Plus } from "@element-plus/icons-vue";
import { UploadProps, UploadRawFile, UploadRequestOptions, UploadUserFile } from "element-plus";
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";

import { FileAPI } from "@/api/file";

import type { ImageUploadProps, UploadModelType, UploadModelValue } from "./type";

const props = defineProps<ImageUploadProps>();
const modelValue = defineModel<UploadModelValue>("modelValue");

const emit = defineEmits(["update:modelValue", "upload-error"]);

const dialogImageUrl = ref("");
const dialogVisible = ref(false);

const fileList = ref<UploadUserFile[]>([]);

// 组件初始化时根据 modelValue 初始化 fileList
const initFileList = () => {
  if (!modelValue.value) {
    fileList.value = [];
    return;
  }

  if (Array.isArray(modelValue.value)) {
    fileList.value = modelValue.value
      .filter((item) => item.key)
      .map((item) => ({
        name: item.key,
        url: item.url,
      }));
  } else {
    fileList.value = [
      {
        name: modelValue.value.key,
        url: modelValue.value.url,
      },
    ];
  }
};

watch(modelValue, () => {
  initFileList();
});

initFileList();

const handleBeforeRemove: UploadProps["beforeRemove"] = (uploadFile, uploadFiles) => {
  if (props.maxFileCount === 1) {
    modelValue.value = undefined;
  } else {
    console.log("uploadFiles", uploadFiles);
    const indexOfUploadfile = uploadFiles.findIndex((item) => item.url === uploadFile.url);
    if (typeof modelValue.value === "string") {
      modelValue.value = undefined;
    } else {
      modelValue.value = (modelValue.value as UploadModelType[]).filter(
        (_, index) => index !== indexOfUploadfile
      );
    }
  }
  return true;
};

// 根据文件类型设置accept属性
const acceptType = computed(() => {
  if (props.accept) return props.accept;
  return ".jpg,.jpeg,.png,.gif,.bmp,.webp";
});

/** 提示文本 */
const tips = computed(() => {
  if (!props.tip) return [`上传图片，大小不超过${props.maxSize || 2}MB`];
  return Array.isArray(props.tip) ? props.tip : [props.tip];
});

/**
 * 自定义文件上传
 */
async function uploadFile(options: UploadRequestOptions): Promise<any> {
  try {
    const data = await FileAPI.uploadFile(options.file);
    if (props.maxFileCount === 1) {
      modelValue.value = data as any;
    } else {
      modelValue.value = [...((modelValue.value ?? []) as any[]), data as any];
    }
  } catch (error: any) {
    emit("upload-error", error.message);
    throw error;
  }
}

/**
 * 预览图片
 */
const handlePictureCardPreview: UploadProps["onPreview"] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!;
  dialogVisible.value = true;
};

/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file: UploadRawFile) {
  const maxSize = (props.maxSize || 2) * 1024 * 1024;
  const maxSizeErrMsg = `上传文件不能大于${props.maxSize || 2}M`;
  if (file.size > maxSize) {
    ElMessage.warning(maxSizeErrMsg);
    return false;
  }
  return true;
}
</script>

<style scoped>
.image-upload-limited {
  :deep(.el-upload.el-upload--picture-card) {
    display: none;
  }
}

.tips-wrapper {
  margin-top: 8px;
}
.tips-wrapper .upload-tip {
  /* margin-top: 8px; */
  line-height: 1.5;
}
.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
