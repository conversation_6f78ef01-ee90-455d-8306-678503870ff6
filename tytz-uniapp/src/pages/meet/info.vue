<route lang="json5">
{
  style: {
    navigationBarTitleText: '约见信息',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { getAppointmentPage } from '@/api/meet/index'
import dayjs from 'dayjs'
defineOptions({
  name: 'MeetInfo',
})

const MeetingStatus = {
  WAIT: '待约见',
  WAIT_START: '待开始',
  MEETING: '约见中',
  END: '已结束',
  REJECT: '不予约见',
} as const

const StatusColorMap = {
  WAIT: 'rgba(77, 77, 77, 1)',
  WAIT_START: 'rgba(73, 166, 253, 1)',
  MEETING: 'rgba(33, 84, 118, 1)',
  END: 'rgba(139, 195, 73, 1)',
  REJECT: 'rgba(239, 65, 66, 1)',
} as Record<string, string>

const StatusIconMap = {
  WAIT: 'wait',
  WAIT_START: 'wait_start',
  MEETING: 'meeting',
  END: 'end',
  REJECT: 'reject',
} as Record<string, string>

const getStatusColor = (status: string): string => StatusColorMap[status]
const search = ref('')
const loading = ref(false)
const refreshing = ref(false) // 新增：下拉刷新状态
const page = reactive({
  pageNum: 1,
  pageSize: 10,
})

const data = reactive<Awaited<ReturnType<typeof getAppointmentPage>>>({
  list: [],
  total: 0,
})

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (data.list.length >= data.total) return 'finished'
  return undefined
})

function handleFilter() {
  page.pageNum = 1
  data.list = []
  data.total = 0
  request(true)
}

function handleDetail(id: number) {
  useNavigate(`/pages/meet/detail`, {
    id,
  })
}

function request(isRefresh = false) {
  loading.value = true
  if (isRefresh) {
    page.pageNum = 1
    data.list = []
    data.total = 0
    uni.showLoading({
      title: '加载中...',
    })
  }
  getAppointmentPage({
    ...page,
    keyword: search.value,
  })
    .then((res) => {
      data.list.push(...res.list)
      data.total = Number(res.total)
      page.pageNum++
    })
    .finally(() => {
      uni.hideLoading()
      loading.value = false
      refreshing.value = false // 重置下拉刷新状态
    })
}

// 新增：下拉刷新处理函数
function handleRefresh() {
  refreshing.value = true
  request(true) // 刷新数据
}

function handleScrollToLower() {
  if (data.list.length >= data.total) return
  if (loading.value) return
  request()
}
request(true)
</script>

<template>
  <PageContainer theme="contrastPage" slot-type="view">
    <view class="flex-1 flex flex-col overflow-hidden">
      <!-- 搜索 -->
      <view class="w-full h-90rpx flex flex-between">
        <view class="flex-1 flex flex-center flex-center bg-white box-border pl-10px">
          <wd-icon name="search" color="#999999" size="16px"></wd-icon>
          <input
            v-model="search"
            type="text"
            class="flex-1 h-full ml-10rpx"
            placeholder="请输入内容关键字"
          />
        </view>
        <view
          class="flex flex-center w-180rpx h-full bg-white rounded-8rpx ml-20rpx"
          @click="handleFilter"
        >
          搜索
        </view>
      </view>
      <!-- 列表 -->
      <scroll-view
        class="mt-10px flex flex-1 overflow-hidden"
        scroll-y
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="handleRefresh"
        @scrolltolower="handleScrollToLower"
      >
        <view
          class="card-item bg-white rounded-4rpx pb-20rpx mb-20rpx"
          v-for="item in data.list"
          :key="item.id"
          @click="handleDetail(item.id)"
        >
          <view
            class="flex items-center p-20rpx text-26rpx border-b-#E5E5E5 border-b-1rpx border-b-solid"
            :style="{ color: getStatusColor(item.appointmentStatus) }"
          >
            <image
              :src="'/static/images/meet/' + StatusIconMap[item.appointmentStatus] + '.png'"
              class="w-35rpx h-35rpx"
            ></image>
            <view class="ml-10rpx">{{ MeetingStatus[item.appointmentStatus] }}</view>
          </view>
          <view class="flex mt-10rpx px-20rpx">
            <view class="text-#A6A6A6 text-26rpx w-[120rpx]">约见部委:</view>
            <view class="text-#333333 text-26rpx ml-10rpx">
              {{ item.appointmentDepartment }}
            </view>
          </view>
          <view class="flex mt-10rpx px-20rpx">
            <view class="text-#A6A6A6 text-26rpx w-[120rpx]">约见时间:</view>
            <view class="text-#333333 text-26rpx ml-10rpx">
              {{ dayjs(item.appointmentStartTime).format('YYYY-MM-DD HH:mm') }} -
              {{ dayjs(item.appointmentEndTime).format('YYYY-MM-DD HH:mm') }}
            </view>
          </view>
          <view class="flex mt-10rpx px-20rpx">
            <view class="text-#A6A6A6 text-26rpx w-[120rpx]">约见原因:</view>
            <view class="text-#333333 text-26rpx ml-10rpx w-[80%] line-clamp-2">
              {{ item.appointmentReason }}
            </view>
          </view>
        </view>
        <wd-loadmore :state="loadMoreState" />
      </scroll-view>
    </view>
  </PageContainer>
</template>

<style scoped></style>
