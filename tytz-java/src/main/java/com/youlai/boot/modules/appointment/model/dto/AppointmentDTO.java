package com.youlai.boot.modules.appointment.model.dto;

import com.youlai.boot.common.enums.HandleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 约见管理表单对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Schema(description = "约见管理表单对象")
@Data
public class AppointmentDTO {

    @Schema(description = "处理状态")
    private HandleEnum handleStatus;

    @Schema(description = "处理意见")
    private String handleComment;

    @Schema(description = "反馈内容")
    private String feedback;
}