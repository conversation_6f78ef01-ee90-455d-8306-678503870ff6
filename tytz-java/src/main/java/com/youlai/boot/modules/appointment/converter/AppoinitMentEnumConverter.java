package com.youlai.boot.modules.appointment.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.youlai.boot.common.enums.AppoinitMentEnum;

public class AppoinitMentEnumConverter implements Converter<AppoinitMentEnum> {

    @Override
    public Class<AppoinitMentEnum> supportJavaTypeKey() {
        return AppoinitMentEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<AppoinitMentEnum> context) {
        AppoinitMentEnum value = context.getValue();
        if (value == null) {
            return new WriteCellData<>("");
        }
        // 使用枚举的toString方法或description字段
        return new WriteCellData<>(value.getLabel());
    }
}