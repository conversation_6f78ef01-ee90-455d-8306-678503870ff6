<template>
  <div class="sidebar-logo-container">
    <router-link :key="+collapse" class="wh-full flex-center" to="/">
      <div class="logo" :style="{ 'background-image': `url(${logo})` }">
        <!-- <transition enter-active-class="animate__animated animate__fadeInLeft">
      
        <img :src="logo" class="w20px h20px" />
        <span v-if="!collapse" class="title">
          {{ defaultSettings.title }}
        </span>
  </router-link>
     
    </transition> -->
      </div>
    </router-link>
  </div>
</template>

<script lang="ts" setup>
import logo from "@/assets/main_logo.png";

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});
</script>

<style lang="scss" scoped>
.sidebar-logo-container {
  width: 100%;
  padding-top: 10px;
  background-color: #ef4142;
  .logo {
    flex-shrink: 0;
    width: 100%;
    height: $navbar-height;
    background-position: center center;
    background-size: cover;

    .title {
      flex-shrink: 0; /* 防止容器在空间不足时缩小 */
      margin-left: 10px;
      font-size: 14px;
      font-weight: bold;
      color: $sidebar-logo-text-color;
    }
  }
}

.layout-top,
.layout-mix {
  .logo {
    width: $sidebar-width;
  }

  &.hideSidebar {
    .logo {
      width: $sidebar-width-collapsed;
    }
  }
}
</style>
