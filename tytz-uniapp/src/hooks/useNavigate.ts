import { objectToUrlParams } from '@/utils/object'
import { getPageConfig } from '@/utils/router'
import { urlToObject } from '@/utils/sting'
import { ACCESS_TOKEN } from '@/const/storage'

const navigateModeMap = {
  navigateTo: uni.navigateTo,
  redirectTo: uni.redirectTo,
  switchTab: uni.switchTab,
  reLaunch: uni.reLaunch,
}

/** 公共的页面跳转函数, 用于跳转页面, 并传递参数 */
/** 公共的页面跳转函数, 用于跳转页面, 并传递参数 */
export const useNavigate = (
  url: ReLaunchOptions['url'],
  params: AnyObject = {},
  mode: keyof typeof navigateModeMap = 'navigateTo',
) => {
  const toUrl = url.split('?')[0]
  Object.assign(params, urlToObject(url))
  const { meta } = getPageConfig(toUrl.slice(1))
  const token = uni.getStorageSync(ACCESS_TOKEN)
  const query = objectToUrlParams(params)
  const link = query ? `${toUrl}?${query}` : toUrl

  if (meta?.login) {
    if (token) {
      return navigateModeMap[mode]({ url: link })
    }
    return useNavigate('/pages/login/index', { redirectTo: encodeURIComponent(link) }, 'navigateTo')
  } else {
    return navigateModeMap[mode]({ url: link })
  }
}
