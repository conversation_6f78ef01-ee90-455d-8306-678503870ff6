<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '通知公告',
  },
}
</route>
<script lang="ts" setup>
import PageContainer from '@/components/PageContainer/index.vue'
import DetailHeader from './_comp/detailHeader.vue'
import { getNoticeDetail } from '@/api/notice/index'
import { calculateDaysAgo } from '@/utils/time'

const props = defineProps<{
  id: string
}>()
const detail = ref<Awaited<ReturnType<typeof getNoticeDetail>>>()
const content = computed(() => {
  return detail.value?.content.replace(
    /<img([^>]*?style=["'][^"']*["'])?([^>]*)>/gi,
    (match, p1, p2) => {
      const adaptiveStyle = 'style="max-width: 100%; height: auto;"'
      if (p1) {
        return `<img${p1} ${adaptiveStyle}>`
      } else if (p2) {
        return `<img${p2} ${adaptiveStyle}>`
      }
      return match
    },
  )
})
onMounted(async () => {
  const res = await getNoticeDetail(props.id)
  detail.value = res
})
</script>
<template>
  <PageContainer>
    <view class="container">
      <view class="page-header">
        <DetailHeader
          :title="detail.title"
          :imageUrl="true"
          :publisher="detail.createBy"
          :commentCount="0"
          :createTime="calculateDaysAgo(detail.createTime)"
        ></DetailHeader>
      </view>
      <rich-text :nodes="content"></rich-text>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}
.page-header {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}
</style>
