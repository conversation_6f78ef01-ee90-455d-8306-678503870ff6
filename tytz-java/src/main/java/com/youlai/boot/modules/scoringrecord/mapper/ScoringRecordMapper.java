package com.youlai.boot.modules.scoringrecord.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.modules.scoringrecord.model.query.ScoringRecordPageQuery;
import com.youlai.boot.modules.scoringrecord.model.vo.ScoringRecordVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 评分记录 Mapper 接口
 */
@Mapper
public interface ScoringRecordMapper extends BaseMapper<ScoringRecord> {

    /**
     * 获取评分记录分页列表
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<ScoringRecordVO> getScoringRecordPage(Page<ScoringRecord> page, ScoringRecordPageQuery queryParams);

    /**
     * 根据会议ID删除评分记录
     *
     * @param meetingId 会议ID
     * @return 影响行数
     */
    int deleteByMeetingId(Long meetingId);

    /**
     * 根据工作ID删除评分记录
     *
     * @param workId 工作ID
     * @return 影响行数
     */
    int deleteByWorkId(Long workId);

    /**
     * 根据活动ID删除评分记录
     *
     * @param activityId 活动ID
     * @return 影响行数
     */
    int deleteByActivityId(Long activityId);

    /**
     * 根据营商材料ID删除评分记录
     *
     * @param businessMaterialsId 营商材料ID
     * @return 影响行数
     */
    int deleteByBusinessMaterialsId(Long businessMaterialsId);

    /**
     * 根据问题ID删除评分记录
     *
     * @param problemId 问题ID
     * @return 影响行数
     */
    int deleteByProblemId(Long problemId);

    /**
     * 获取评分记录导出列表（带统计数据）
     *
     * @param queryParams 查询参数
     * @return 评分记录列表（带统计数据）
     */
    List<ScoringRecordVO> getExportList(ScoringRecordPageQuery queryParams);
}