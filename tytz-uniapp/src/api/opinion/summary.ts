import request from '@/utils/request'

/**
 * DataOpinionVO
 */
export interface DataOpinionVO {
  list?: OpinionVO[]
  total?: number
}

/**
 * 意见征集视图对象
 *
 * OpinionVO
 */
export interface OpinionVO {
  /**
   * 联系方式
   */
  contact?: string
  /**
   * 意见内容
   */
  content?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 所属单位
   */
  department?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 商会会员名称
   */
  memberName?: string
}

/**
 * 意见征集分页列表
 * @param params
 * @returns
 */
export const getOpinionPage = (params: { pageNum: number; pageSize: number; keyword?: string }) => {
  return request<DataOpinionVO>('/api/v1/opinions/my/page', {
    method: 'GET',
    data: params,
  })
}

/**
 * OpinionForm
 */
export interface OpinionForm {
  /**
   * 联系方式
   */
  contact?: string
  /**
   * 意见内容
   */
  content: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 所属单位
   */
  department?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 商会会员名称
   */
  memberName?: string
}

/**
 * 意见详情
 * @param id
 * @returns
 */
export const getOpinionDetail = (id: string) => {
  return request<OpinionForm>('/api/v1/opinions/' + id + '/form', {
    method: 'GET',
  })
}
