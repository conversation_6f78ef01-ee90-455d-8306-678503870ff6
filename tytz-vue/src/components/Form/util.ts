import type { ComponentTypeMap } from "@/components/CustomComponent/type";
import { replacePlaceholders } from "@/utils/string";

import type { FormItemUnionType } from "./type";

/**
 * 获取提示文案
 * @param type 提示类型
 * @param label 标签
 * @returns { string }
 */
export function getPrefixTips(type: keyof ComponentTypeMap, label = "") {
  if (["input"].includes(type)) {
    return `请填写${label}`;
  } else if (["select", "radio", "checkbox", "date-picker"].includes(type)) {
    return `请选择${label}`;
  } else {
    return "该选项为必填";
  }
}

/**
 * 初始化组件的placeholder
 * @returns {string} 提示信息
 */
export function initPlaceholder(item: FormItemUnionType) {
  if (item.props?.placeholder === void 0) {
    return getPrefixTips(item.is, item.formItem.label);
  } else {
    return replacePlaceholders(item.props.placeholder, "${1}", item.formItem.label || "");
  }
}

export function initRules(item: FormItemUnionType) {
  if (!item.formItem.required) return;
  const rules = Array.isArray(item.formItem.rules)
    ? [...item.formItem.rules]
    : item.formItem.rules === undefined
      ? []
      : [item.formItem.rules];

  rules.unshift({
    required: true,
    message: getPrefixTips(item.is, item.formItem.label),
    trigger: ["blur"],
  });
  return rules;
}
