package com.youlai.boot.modules.notice.controller;

import com.youlai.boot.modules.notice.service.TyTzNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.youlai.boot.modules.notice.model.form.TyTzNoticeForm;
import com.youlai.boot.modules.notice.model.query.TyTzNoticeQuery;
import com.youlai.boot.modules.notice.model.vo.TyTzNoticeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * 通知公告前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Tag(name = "通知公告接口")
@RestController
@RequestMapping("/api/v1/tytzNotices")
@RequiredArgsConstructor
public class TyTzNoticeController {

    private final TyTzNoticeService tyTzNoticeService;

    @Operation(summary = "通知公告分页列表")
    @GetMapping("/page")
//    @PreAuthorize("@ss.hasPerm('modules.notice:notice:query')")
    public PageResult<TyTzNoticeVO> getNoticePage(TyTzNoticeQuery queryParams ) {
        IPage<TyTzNoticeVO> result = tyTzNoticeService.getNoticePage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增通知公告")
    @PostMapping
//    @PreAuthorize("@ss.hasPerm('modules.notice:notice:add')")
    public Result<Boolean> saveNotice(@RequestBody @Valid TyTzNoticeForm formData ) {
//        boolean result = tyTzNoticeService.tyTzNoticeService(formData);
//        return Result.judge(result);
        return Result.success(tyTzNoticeService.saveNotice(formData));
    }

    @Operation(summary = "获取通知公告表单数据")
    @GetMapping("/{id}/form")
//    @PreAuthorize("@ss.hasPerm('modules.notice:notice:edit')")
    public Result<TyTzNoticeForm> getNoticeForm(
        @Parameter(description = "通知公告ID") @PathVariable Long id
    ) {
        TyTzNoticeForm formData = tyTzNoticeService.getNoticeFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改通知公告")
    @PutMapping(value = "/{id}")
//    @PreAuthorize("@ss.hasPerm('modules.notice:notice:edit')")
    public Result<Void> updateNotice(
            @Parameter(description = "通知公告ID") @PathVariable Long id,
            @RequestBody @Validated TyTzNoticeForm formData
    ) {
        boolean result = tyTzNoticeService.updateNotice(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除通知公告")
    @DeleteMapping("/{ids}")
//    @PreAuthorize("@ss.hasPerm('modules.notice:notice:delete')")
    public Result<Void> deleteNotices(
        @Parameter(description = "通知公告ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = tyTzNoticeService.deleteNotices(ids);
        return Result.judge(result);
    }
}
