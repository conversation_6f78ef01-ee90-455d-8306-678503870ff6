package com.youlai.boot.modules.welfare.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.modules.welfare.model.entity.Welfare;
import com.youlai.boot.modules.welfare.model.form.WelfareForm;
import com.youlai.boot.modules.welfare.model.query.WelfareQuery;
import com.youlai.boot.modules.welfare.model.vo.WelfareVO;

/**
 * 公益信息服务接口
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface WelfareService extends IService<Welfare> {

    /**
     * 获取公益信息分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<WelfareVO> getWelfarePage(WelfareQuery queryParams);

    /**
     * 获取公益信息表单数据
     *
     * @param id 公益活动ID
     * @return 表单数据
     */
    WelfareForm getWelfareFormData(Long id);

    /**
     * 新增公益信息
     *
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean saveWelfare(WelfareForm formData);

    /**
     * 修改公益信息
     *
     * @param id 公益活动ID
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean updateWelfare(Long id, WelfareForm formData);

    /**
     * 删除公益信息
     *
     * @param ids 公益活动ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteWelfares(String ids);
} 