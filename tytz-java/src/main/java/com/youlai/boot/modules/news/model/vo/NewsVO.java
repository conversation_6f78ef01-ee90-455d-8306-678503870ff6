package com.youlai.boot.modules.news.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youlai.boot.common.enums.PublishStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 新闻资讯视图对象
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Schema(description = "新闻资讯视图对象")
@Data
public class NewsVO {

    @Schema(description = "新闻ID")
    private Long id;

    @Schema(description = "新闻标题")
    private String title;

    @Schema(description = "新闻内容")
    private String content;

    @Schema(description = "发布状态（WAIT: 待审核, PUSH: 审核通过, CANCEL: 审核驳回）")
    private PublishStatusEnum publishStatus;

    @Schema(description = "附件JSON数据")
    private String attachments;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}