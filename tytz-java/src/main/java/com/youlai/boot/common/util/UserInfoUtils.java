package com.youlai.boot.common.util;

import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.system.model.vo.UserProfileVO;
import com.youlai.boot.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserInfoUtils {

        private static UserService userService;

        @Autowired
        public void setUserService(UserService userService) {
            UserInfoUtils.userService = userService;
        }

    public static UserProfileVO getUserInfo() {
        UserProfileVO userProfile = userService.getUserProfile(SecurityUtils.getUserId());
        if (userProfile == null) {
            throw new RuntimeException("用户信息不存在");
        }
        return userProfile;
    }
}
