package com.youlai.boot.modules.work.converter;

import org.mapstruct.Mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.youlai.boot.modules.work.model.form.KeyWorkForm;
import org.mapstruct.Mapping;

/**
 * 年度重点工作对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Mapper(componentModel = "spring")
public interface KeyWorkConverter {
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    KeyWorkForm toForm(KeyWork entity);

    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    KeyWork toEntity(KeyWorkForm formData);
} 