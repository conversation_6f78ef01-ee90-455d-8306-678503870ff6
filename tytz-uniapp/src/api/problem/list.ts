import request from '@/utils/request'

/**
 * DataProblemVO
 */
export interface DataProblemVO {
  list?: ProblemVO[]
  total?: number
}

/**
 * 营商环境问题视图对象
 *
 * ProblemVO
 */
export interface ProblemVO {
  /**
   * 采纳人
   */
  adoptBy?: string
  /**
   * 采纳意见
   */
  adoptContent?: string
  /**
   * 采纳状态
   */
  adoptStatus?: Status
  /**
   * 采纳时间
   */
  adoptTime?: string
  /**
   * 营商环境类别
   */
  businessType?: string
  /**
   * 营商环境问题内容
   */
  content?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 所属部门
   */
  department?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 批示人
   */
  instructionBy?: string
  /**
   * 批示内容
   */
  instructionContent?: string
  /**
   * 批示状态
   */
  instructionStatus?: Status
  /**
   * 批示时间
   */
  instructionTime?: string
  /**
   * 领导批示
   */
  leaderInstruction?: string
  /**
   * 商会会员名称
   */
  memberName?: string
  /**
   * 提交时间
   */
  submitTime?: string
  /**
   * 营商环境标题
   */
  title?: string
}

/**
 * 采纳状态
 *
 * 批示状态
 */
export enum Status {
  Pass = 'PASS',
  Reject = 'REJECT',
  Wait = 'WAIT',
}
/**
 * 获取营商环境问题列表
 * @param params
 * @returns
 */
export const getProblemPage = (params: { pageNum: number; pageSize: number; title?: string }) => {
  return request<DataProblemVO>('/api/v1/problems/my/page', {
    method: 'GET',
    data: params,
  })
}

/**
 * ProblemForm
 */
export interface ProblemForm {
  /**
   * 采纳人
   */
  adoptBy?: string
  /**
   * 采纳意见
   */
  adoptContent?: string
  /**
   * 采纳状态
   */
  adoptStatus?: Status
  /**
   * 营商环境类别
   */
  businessType: string
  /**
   * 营商环境问题内容
   */
  content: string
  /**
   * 所属部门
   */
  department?: string
  /**
   * 批示人
   */
  instructionBy?: string
  /**
   * 批示内容
   */
  instructionContent?: string
  /**
   * 批示状态
   */
  instructionStatus?: Status
  /**
   * 领导批示
   */
  leaderInstruction?: string
  /**
   * 商会会员名称
   */
  memberName?: string
  /**
   * 提交时间
   */
  submitTime?: string
  /**
   * 营商环境标题
   */
  title: string
}
/**
 * 获取营商环境问题表单
 * @param id
 * @returns
 */
export const getProblemForm = (id: string) => {
  return request<ProblemForm>(`/api/v1/problems/${id}/form`, {
    method: 'GET',
  })
}
