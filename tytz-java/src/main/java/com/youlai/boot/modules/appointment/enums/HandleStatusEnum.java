package com.youlai.boot.modules.appointment.enums;

import lombok.Getter;

/**
 * 约见处理状态枚举
 * 
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
public enum HandleStatusEnum {
    
    APPROVED(1, "予以约见"),
    REJECTED(2, "不予约见");
    
    private final Integer value;
    private final String label;
    
    HandleStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
    
    public static String getLabelByValue(Integer value) {
        for (HandleStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status.getLabel();
            }
        }
        return null;
    }
} 