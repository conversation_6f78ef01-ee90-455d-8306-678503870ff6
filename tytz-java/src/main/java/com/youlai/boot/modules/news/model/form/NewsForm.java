package com.youlai.boot.modules.news.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

import com.youlai.boot.common.enums.PublishStatusEnum;

/**
 * 新闻资讯表单对象
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Schema(description = "新闻资讯表单对象")
@Data
public class NewsForm {

    @Schema(description = "新闻ID")
    private Long id;

    @Schema(description = "新闻标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "新闻标题不能为空")
    private String title;

    @Schema(description = "新闻内容")
    private String content;

    @Schema(description = "发布状态（WAIT: 待审核, PUSH: 审核通过, CANCEL: 审核驳回）")
    private PublishStatusEnum publishStatus;

    @Schema(description = "附件列表")
    private List<FileDTO> attachments;

    @Schema(description = "创建人")
    private String createBy;

    @Data
    @Schema(description = "文件信息")
    public static class FileDTO {

        @Schema(description = "文件名称")
        private String name;

        @Schema(description = "文件URL")
        private String url;
    }
}