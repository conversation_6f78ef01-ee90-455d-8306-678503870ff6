package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 会议类型枚举
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
public enum MeetingTypeEnum implements IBaseEnum<String> {

    MEMBER("MEMBER", "参加会员（代表）大会"),
    PRESIDENT("PRESIDENT", "参加会长会议"),
    DIRECTOR("DIRECTOR", "参加理事会会议");


    private final String value;

    private final String label;

    MeetingTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}