<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '新闻资讯',
  },
}
</route>
<script lang="ts" setup>
import PageContainer from '@/components/PageContainer/index.vue'
import DetailHeader from './_comp/detailHeader.vue'
import { getNewsDetail, getNewsPage } from '@/api/news'
import { calculateDaysAgo } from '@/utils/time'
const props = defineProps<{
  id: string
}>()
const detail = ref<Awaited<ReturnType<typeof getNewsDetail>>>()
const page = reactive({
  pageNum: 1,
  pageSize: 100,
})
const data = reactive<Awaited<ReturnType<typeof getNewsPage>>>({
  list: [],
  total: 0,
})
const prevDetail = ref<Awaited<ReturnType<typeof getNewsDetail>>>()
const nextDetail = ref<Awaited<ReturnType<typeof getNewsDetail>>>()
const content = computed(() => {
  return detail.value?.content.replace(
    /<img([^>]*?style=["'][^"']*["'])?([^>]*)>/gi,
    (match, p1, p2) => {
      const adaptiveStyle = 'style="max-width: 100%; height: auto;"'
      if (p1) {
        return `<img${p1} ${adaptiveStyle}>`
      } else if (p2) {
        return `<img${p2} ${adaptiveStyle}>`
      }
      return match
    },
  )
})

function request() {
  page.pageNum = 1
  data.list = []
  data.total = 0
  getNewsPage({
    ...page,
  }).then(async (res: any) => {
    data.list.push(...res.records)
    data.total = Number(res.total)
    const currentIndex = data.list.findIndex((item) => item.id === props.id)
    if (currentIndex !== -1) {
      prevDetail.value = data.list[currentIndex - 1] || null
      nextDetail.value = data.list[currentIndex + 1] || null
    }
  })
}
const handleDetail = (e: any) => {
  uni.navigateTo({
    url: `/pages/news/detail?id=${e}`,
  })
}

onMounted(async () => {
  const res = await getNewsDetail(props.id)
  detail.value = res
  await request()
})
</script>
<template>
  <PageContainer>
    <view class="container">
      <view class="page-header">
        <DetailHeader
          :title="detail.title"
          :imageUrl="true"
          :publisher="detail.createBy"
          :commentCount="0"
          :createTime="calculateDaysAgo(detail.createTime)"
        ></DetailHeader>
      </view>
      <view class="page-content">
        <rich-text :nodes="content"></rich-text>
      </view>
      <view class="mt-3 text-27rpx text-#666666">
        <view v-if="prevDetail" @click="handleDetail(prevDetail.id)">
          上一篇：{{ prevDetail ? prevDetail.title : '无' }}
        </view>
        <view v-if="nextDetail" @click="handleDetail(nextDetail.id)">
          下一篇：{{ nextDetail ? nextDetail.title : '无' }}
        </view>
      </view>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}
.page-header {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.page-content {
  ::v-deep img {
    width: 80%;
  }
}
</style>
