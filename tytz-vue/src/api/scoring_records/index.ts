import request from "@/utils/request";

const API_PREFIX = "/api/v1/scoring-records";

/**
 * 评价表分页查询对象
 */
export interface scoringRecordPageQuery extends PageQuery {
  /** 年度 */
  year?: string;
  /** 会员名称 */
  memberName?: string;
  /** 所属单位 */
  department?: string;
}
/**评价表信息分页列表 */
export interface scoringRecordPageVO {
  /** 主键 */
  id: string;

  /** 年度 */
  year?: string;

  /** 会员名称 */
  memberName?: string;

  /** 所属部门 */
  department?: string;

  /** 参加活动（次）*/
  activeCount: string;

  /** 参加会议（次）*/
  meetingCount: string;

  /** 年度重点工作（次） */
  keyWorkCount?: string;

  /** 营商环境问题报送（次） */
  environmentCount?: string;

  /** 总得分 */
  totalScore?: string;
}

/** 评分表分页列表 */
export const scoringRecordPageApi = (queryParams: scoringRecordPageQuery) => {
  return request<any, PageResult<scoringRecordPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/**
 * 评价表分页查询对象
 */
export interface scoringRecordDetailQuery extends PageQuery {
  /** 年度 */
  year?: string;
  /** 会员名称 */
  memberId?: string;
  /** 评分类型 */
  scoringTypes?: string;
  /**所属单位 */
  department?: string;
  ids?: string[];
}

/** 评分表详情 */
export const scoringRecordDetailApi = (queryParams: scoringRecordDetailQuery) => {
  return request<any, any>({
    url: `${API_PREFIX}/detail/page`,
    params: queryParams,
    method: "get",
  });
};

/**导出 */
export const scoringRecordDetailExportApi = (queryParams: any) => {
  return request<any, any>({
    url: `${API_PREFIX}/export`,
    method: "POST",
    data: queryParams,
    responseType: "blob",
  });
};
