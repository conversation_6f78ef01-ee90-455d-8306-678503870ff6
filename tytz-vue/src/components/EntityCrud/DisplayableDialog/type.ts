export type DisplayableItem = {
  /** 表单项属性名 */
  prop: string;
  /** 表单项标签文本 */
  label: string;
  /** 表单项类型 */
  type: "text" | "images" | "documents" | "rich-text" | "date-range" | "radio";
  /** 表单项占位提示文本 */
  placeholder?: string;
  /**表单宽度 */
  width?: string;
  /** 表单项格式化函数 */
  formatter?: (value: any) => string;
};

export interface UseDisplayableDialogOptions {
  /** 表单配置项 */
  displayItems: DisplayableItem[];
  /** 表单宽度 */
  width?: string | number;
  /** 自定义标题 */
  customTitle?: string;
}

export type DisplayableDialogProps = {
  title?: string;
  width?: string;
  visible: boolean;
  displayData: Record<string, any> | null;
  displayItems: DisplayableItem[];
};
