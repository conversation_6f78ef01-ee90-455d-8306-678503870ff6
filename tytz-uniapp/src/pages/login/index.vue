<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { getAuthCaptcha, authLogin } from '@/api/login'
import { TOKEN_TYPE, ACCESS_TOKEN, REFRESH_TOKEN } from '@/const/storage'
import { getUserInfo } from '@/api/user'
import { useNewUserStoreHook } from '@/store/modules/user'

const userStore = useNewUserStoreHook()

defineOptions({
  name: 'Login',
})

const props = defineProps<{
  redirectTo?: ReLaunchOptions['url']
}>()

const captchaKey = ref('')
const captchaBase64 = ref('')

const formData = reactive({
  username: '',
  password: '',
  captchaCode: '',
})

/**
 * 获取验证码
 */
function getCaptcha() {
  getAuthCaptcha().then((res) => {
    captchaKey.value = res.captchaKey
    captchaBase64.value = res.captchaBase64
  })
}
getCaptcha()

async function handleLogin() {
  const params = {
    username: formData.username,
    password: formData.password,
    captchaKey: captchaKey.value,
    captchaCode: formData.captchaCode,
  }
  uni.showLoading({
    title: '登录中...',
  })
  try {
    const res = await authLogin(params)
    uni.setStorageSync(TOKEN_TYPE, res.tokenType)
    uni.setStorageSync(ACCESS_TOKEN, res.accessToken)
    uni.setStorageSync(REFRESH_TOKEN, res.refreshToken)
    const user = await getUserInfo()
    userStore.setUserInfo(user)
    userStore.setRole(user.roles[0])
    if (props.redirectTo) {
      useNavigate(props.redirectTo, {}, 'redirectTo')
    } else {
      uni.navigateBack()
    }
  } catch (error) {
    getCaptcha()
  } finally {
    uni.hideLoading()
  }
}
</script>

<template>
  <PageContainer theme="login">
    <view class="p-20px flex-1">
      <view class="text-#222222 text-48rpx mt-18vh">你好，</view>
      <view class="text-#222222 text-48rpx">天涯区智慧统战平台</view>
      <view class="mt-40rpx">
        <view class="input-box flex">
          <view class="w-70rpx h-90rpx flex items-center justify-center">
            <wd-icon name="user" color="#999999" size="22px"></wd-icon>
          </view>
          <input
            v-model="formData.username"
            class="flex-1 h-full"
            type="text"
            placeholder="请输入用户手机号"
            placeholder-class="text-#999999"
          />
        </view>

        <view class="input-box flex">
          <view class="w-70rpx h-90rpx flex items-center justify-center">
            <wd-icon name="lock-on" color="#999999" size="22px"></wd-icon>
          </view>
          <input
            v-model="formData.password"
            class="flex-1 h-full"
            password
            placeholder="请输入密码"
            placeholder-class="text-#999999"
          />
        </view>
        <view class="input-box flex">
          <view class="w-70rpx h-90rpx flex items-center justify-center">
            <wd-icon name="secured" color="#999999" size="22px"></wd-icon>
          </view>
          <input
            v-model="formData.captchaCode"
            class="flex-1 h-full"
            type="text"
            placeholder="请输入验证码"
            placeholder-class="text-#999999"
          />
          <view class="w-230rpx h-90rpx flex items-center justify-center">
            <image :src="captchaBase64" mode="widthFix" @click="getCaptcha" />
          </view>
        </view>
      </view>
      <view
        class="w-full h-90rpx flex items-center justify-center mt-40rpx bg-#EF4142 text-white"
        @click="handleLogin"
      >
        登录
      </view>
    </view>
  </PageContainer>
</template>

<style lang="scss" scoped>
.input-box {
  width: 100%;
  height: 90rpx;
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 10rpx;
  + .input-box {
    margin-top: 20rpx;
  }
}
</style>
