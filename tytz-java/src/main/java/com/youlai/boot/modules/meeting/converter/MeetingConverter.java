package com.youlai.boot.modules.meeting.converter;

import org.mapstruct.Mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.meeting.model.entity.Meeting;
import com.youlai.boot.modules.meeting.model.form.MeetingForm;
import org.mapstruct.Mapping;

/**
 * 会议管理对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper(componentModel = "spring")
public interface MeetingConverter {
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    MeetingForm toForm(Meeting entity);

    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    Meeting toEntity(MeetingForm formData);
} 