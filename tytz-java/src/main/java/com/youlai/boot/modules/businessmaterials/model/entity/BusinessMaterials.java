package com.youlai.boot.modules.businessmaterials.model.entity;

import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;

/**
 * 营商材料实体对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@TableName("tsz_business_materials")
public class BusinessMaterials extends BaseEntity {

    /**
     * 材料名称
     */
    private String title;
    /**
     * 材料内容
     */
    private String content;
    /**
     * 附件JSON数据，包含文件名、路径等信息
     */
    private String attachments;
    /**
     * 提交人ID
     */
    private Long submitBy;
    /**
     * 活动开始时间
     */
    private LocalDateTime activeStartTime;
    /**
     * 活动结束时间
     */
    private LocalDateTime activeEndTime;
    /**
     * 创建人ID
     */
    private Long createBy;
    /**
     * 更新人ID
     */
    private Long updateBy;
    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
}
