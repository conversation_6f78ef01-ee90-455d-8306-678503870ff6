package com.youlai.boot.modules.problem.model.vo;

import com.youlai.boot.common.enums.AdoptStatusEnum;
import com.youlai.boot.common.enums.InstructionStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 营商环境问题视图对象
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Schema(description = "营商环境问题视图对象")
@Data
public class ProblemVO implements Serializable {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "营商环境标题")
    private String title;

    @Schema(description = "营商环境问题内容")
    private String content;

    @Schema(description = "营商环境类别")
    private String businessType;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "商会会员id")
    private String memberId;

    @Schema(description = "商会会员名称")
    private String memberName;

    @Schema(description = "所属部门")
    private String department;

    @Schema(description = "采纳状态")
    private AdoptStatusEnum adoptStatus;

    @Schema(description = "采纳意见")
    private String adoptContent;

    @Schema(description = "采纳人")
    private String adoptBy;

    @Schema(description = "采纳时间")
    private LocalDateTime adoptTime;

    @Schema(description = "批示状态")
    private InstructionStatusEnum instructionStatus;

    @Schema(description = "批示内容")
    private String instructionContent;

    @Schema(description = "批示人")
    private String instructionBy;

    @Schema(description = "批示时间")
    private LocalDateTime instructionTime;

    @Schema(description = "领导批示")
    private String leaderInstruction;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 