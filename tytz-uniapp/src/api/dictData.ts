import request from '@/utils/request'

/**
 * 获取字典数据列表
 * @param dictCode 字典编码
 * @returns
 */
export const getDictDataOptions = (dictCode: string) => {
  return request<
    {
      value: string
      label: string
      tag: string
      children: Array<{
        value: string
        label: string
        tag: string
        children: any[]
      }>
    }[]
  >(`/api/v1/dict-data/${dictCode}/options`, {
    method: 'GET',
  })
}
