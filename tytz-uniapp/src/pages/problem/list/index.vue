<route lang="json5">
{
  style: {
    navigationBarTitleText: '已报问题',
  },
  meta: {
    login: true,
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import { getProblemPage } from '@/api/problem/list'
defineOptions({
  name: 'ProblemList',
})

const search = ref('')
const loading = ref(false)
const refreshing = ref(false) // 新增：下拉刷新状态
const page = reactive({
  pageNum: 1,
  pageSize: 10,
})

const data = reactive<Awaited<ReturnType<typeof getProblemPage>>>({
  list: [],
  total: 0,
})

const statusEnum = {
  WAIT: {
    label: '待处理',
    color: '#808080',
  },
  PASS: {
    label: '已采纳',
    color: '#8BC349',
  },
  REJECT: {
    label: '不予采纳',
    color: '#EF4142',
  },
}

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (data.list.length >= data.total) return 'finished'
  return undefined
})

function handleFilter() {
  page.pageNum = 1
  data.list = []
  data.total = 0
  request(true)
}

function handleDetail(id: number) {
  useNavigate(`/pages/problem/list/detail`, {
    id,
  })
}

function request(isRefresh = false) {
  loading.value = true
  if (isRefresh) {
    page.pageNum = 1
    data.list = []
    data.total = 0
    uni.showLoading({
      title: '加载中...',
    })
  }
  getProblemPage({
    ...page,
    title: search.value,
  })
    .then((res) => {
      data.list.push(...res.list)
      data.total = Number(res.total)
      page.pageNum++
    })
    .finally(() => {
      uni.hideLoading()
      loading.value = false
      refreshing.value = false // 重置下拉刷新状态
    })
}
// 新增：下拉刷新处理函数
function handleRefresh() {
  refreshing.value = true
  request(true) // 刷新数据
}
function handleScrollToLower() {
  if (data.list.length >= data.total) return
  if (loading.value) return
  request()
}
request(true)
</script>

<template>
  <PageContainer theme="contrastPage" slot-type="view">
    <view class="flex-1 flex flex-col overflow-hidden">
      <!-- 搜索 -->
      <view class="w-full h-90rpx flex flex-between">
        <view class="flex-1 flex flex-center flex-center bg-white box-border pl-10px">
          <wd-icon name="search" color="#999999" size="16px"></wd-icon>
          <input
            v-model="search"
            type="text"
            class="flex-1 h-full ml-10rpx"
            placeholder="请输入问题标题"
          />
        </view>
        <view
          class="flex flex-center w-180rpx h-full bg-white rounded-8rpx ml-20rpx"
          @click="handleFilter"
        >
          搜索
        </view>
      </view>
      <!-- 列表 -->
      <scroll-view
        class="mt-10px flex flex-1 overflow-hidden"
        scroll-y
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="handleRefresh"
        @scrolltolower="handleScrollToLower"
      >
        <view
          class="card-item bg-white rounded-4rpx pb-20rpx"
          v-for="item in data.list"
          :key="item.id"
          @click="handleDetail(item.id)"
        >
          <view
            class="flex items-center justify-end p-20rpx text-26rpx border-b-#E5E5E5 border-b-1rpx border-b-solid"
            :style="{ color: statusEnum[item.adoptStatus]?.color }"
          >
            {{ statusEnum[item.adoptStatus]?.label || '-' }}
          </view>
          <view class="line-clamp-2 box-border px-20rpx mt-20rpx text-#333333 text-30rpx">
            {{ item.title }}
          </view>
          <view class="flex mt-20rpx px-20rpx">
            <view class="text-#A6A6A6 text-26rpx">营商环境类别:</view>
            <view class="text-#333333 text-26rpx ml-10rpx">{{ item.businessType }}</view>
          </view>
          <view class="flex mt-10rpx px-20rpx">
            <view class="text-#A6A6A6 text-26rpx">报送时间:</view>
            <view class="text-#333333 text-26rpx ml-10rpx">{{ item.submitTime }}</view>
          </view>
        </view>
        <wd-loadmore :state="loadMoreState" />
      </scroll-view>
    </view>
  </PageContainer>
</template>

<style scoped lang="scss">
.card-item {
  + .card-item {
    margin-top: 20rpx;
  }
}
</style>
