package com.youlai.boot.modules.scoringrecord.converter;

import com.youlai.boot.modules.scoringrecord.model.ScoringRecord;
import com.youlai.boot.modules.scoringrecord.model.dto.ScoringRecordDTO;
import com.youlai.boot.modules.scoringrecord.model.form.ScoringRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 评分记录转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ScoringRecordConverter {

    /**
     * 表单对象转实体对象
     */
    ScoringRecord formToEntity(ScoringRecordForm form);

    /**
     * 实体对象转DTO对象
     */
    ScoringRecordDTO entityToDTO(ScoringRecord entity);
} 