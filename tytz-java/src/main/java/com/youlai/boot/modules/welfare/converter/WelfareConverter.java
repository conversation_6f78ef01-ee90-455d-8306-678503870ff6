package com.youlai.boot.modules.welfare.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.welfare.model.entity.Welfare;
import com.youlai.boot.modules.welfare.model.form.WelfareForm;
import com.youlai.boot.modules.welfare.model.vo.WelfareVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 公益信息对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Mapper(componentModel = "spring")
public interface WelfareConverter {

    /**
     * 实体转视图对象
     *
     * @param entity 实体
     * @return 视图对象
     */
    WelfareVO entity2Vo(Welfare entity);

    /**
     * 实体转表单对象
     *
     * @param entity 实体
     * @return 表单对象
     */
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    WelfareForm entity2Form(Welfare entity);

    /**
     * 表单对象转实体
     *
     * @param form 表单对象
     * @return 实体
     */
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    Welfare form2Entity(WelfareForm form);
} 