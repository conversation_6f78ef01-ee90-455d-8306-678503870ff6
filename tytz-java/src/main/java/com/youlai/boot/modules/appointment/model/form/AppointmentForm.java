package com.youlai.boot.modules.appointment.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.youlai.boot.common.enums.AppoinitMentEnum;
import com.youlai.boot.common.enums.HandleEnum;

/**
 * 约见管理表单对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Schema(description = "约见管理表单对象")
@Data
public class AppointmentForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;
    
    @Schema(description = "约见人名称")
    private String appointmentName;
    
    @Schema(description = "所属单位")
    private String appointmentUnit;
    
    @Schema(description = "联系方式")
    private String appointmentContact;
    
    @Schema(description = "被约见部委")
    private String appointmentDepartment;
    
    @Schema(description = "约见原因")
    @NotBlank(message = "约见原因不能为空")
    private String appointmentReason;
    
    @Schema(description = "约见开始时间")
    @NotNull(message = "约见开始时间不能为空")
    private LocalDateTime appointmentStartTime;
    
    @Schema(description = "约见结束时间")
    @NotNull(message = "约见结束时间不能为空")
    private LocalDateTime appointmentEndTime;
    
    @Schema(description = "处理状态")
    private HandleEnum handleStatus;

    @Schema(description = "约见状态")
    private AppoinitMentEnum appointmentStatus;
    
    @Schema(description = "处理意见")
    private String handleComment;
    
    @Schema(description = "约见反馈")
    private String feedback;

    @Schema(description = "商会会员id")
    private Long memberId;

    @Schema(description = "会员身份")
    private String memberRole;

} 