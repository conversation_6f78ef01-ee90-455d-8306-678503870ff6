package com.youlai.boot.modules.meeting.model.form;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import java.util.List;

import com.youlai.boot.common.enums.MeetingTypeEnum;

import jakarta.validation.constraints.*;

/**
 * 会议管理表单对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@Schema(description = "会议管理表单对象")
public class MeetingForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "会议名称")
    @NotBlank(message = "会议名称不能为空")
    @Size(max=100, message="会议名称长度不能超过100个字符")
    private String title;

    @Schema(description = "会议类型")
    @NotNull(message = "会议类型不能为空")
    private MeetingTypeEnum meetingType;

//    @Schema(description = "组织者")
//    @NotBlank(message = "组织者不能为空")
//    @Size(max=50, message="组织者长度不能超过50个字符")
//    private String department;

    @Schema(description = "参会人员")
    @NotNull(message = "参会人员不能为空")
    private List<ParticipantDTO> participants;

    @Schema(description = "会议开始时间")
    @NotNull(message = "会议开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "会议结束时间")
    @NotNull(message = "会议结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "会议内容")
    private String content;

    @Schema(description = "附件")
    private List<FileDTO> attachments;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ParticipantDTO {
        @Schema(description = "用户ID")
        private Long id;

        @Schema(description = "用户名")
        private String businessName;

        @Schema(description = "用户信息")
        private String businessMember;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileDTO {
        @Schema(description = "文件url")
        String url;

        @Schema(description = "文件名称")
        String name;
    }
} 