package com.youlai.boot.modules.work.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.work.model.query.KeyWorkQuery;
import com.youlai.boot.modules.work.model.vo.KeyWorkVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 年度重点工作Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Mapper
public interface KeyWorkMapper extends BaseMapper<KeyWork> {

    /**
     * 获取年度重点工作分页数据
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return 年度重点工作分页数据
     */
    Page<KeyWorkVO> getKeyWorkPage(Page<KeyWorkVO> page, KeyWorkQuery queryParams);
} 