<template>
  <el-select
    v-model="modelValue"
    filterable
    remote
    reserve-keyword
    remote-show-suffix
    :value-key="searchFieldName"
    :remote-method="remoteFetchMethod"
    :loading="loading"
    style="width: 100%"
  >
    <template v-if="itemIsPrimitive">
      <el-option v-for="item in options" :key="item" :label="item" :value="item">
        {{ item }}
      </el-option>
    </template>

    <template v-else>
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
        {{ item.label }}
      </el-option>
    </template>
  </el-select>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from "vue";

const props = withDefaults(
  defineProps<{
    /** 远程接口 */
    remoteMethods: () => Promise<any>;
    /** 搜索字段 */
    searchFieldName: string;
    /** label显示对应的字段名 */
    labelField?: string;
    /** value 字段 */
    valueField?: string;
    /** 是否有分页 */
    hasPagination?: boolean;
    /** 每页条数 */
    pageSize?: number;
    /** 立即查询一次数据（用在绑定值为对象但传入值为字符串的情况） */
    immediateFetch?: boolean;
  }>(),
  {
    pageSize: 100,
  }
);

/** v-model绑定的值 */
const modelValue = defineModel<any>();

const loading = ref(false);

const options = ref<Array<any>>([]);

// const fetched = ref(false);

/** option数据项是否是原始值 */
const itemIsPrimitive = computed(() => {
  return (
    options.value.length > 0 &&
    typeof options.value[0] !== "object" &&
    typeof options.value[0] !== "function"
  );
});

/**
 * 截取数据（数组数据项大于某个长度时，只取数组前n项）
 */
const optionsSlice = (options: Array<any>, maxLength: number = props.pageSize) => {
  if (options.length <= maxLength) return options;
  return options.slice(0, maxLength);
};

/** 远程搜索方法 */
const remoteFetchMethod = (query: string) => {
  // 不需要分页，一次性获取所有的数据
  // if (fetched.value && !props.hasPagination) return;

  loading.value = true;
  const params: Record<string, any> = {
    [props.searchFieldName]: query,
  };
  if (props.hasPagination) {
    params["pageNo"] = 1;
    params["pageSize"] = props.pageSize;
  }
  props
    /*  .remoteMethods(params) */
    .remoteMethods()
    .then((res) => {
      let resList: Array<any> = [];
      // 判断 res 是否含有 total 和 list 属性
      if ("total" in res && "list" in res) {
        resList = optionsSlice(res.list as Array<any>);
      } else {
        resList = optionsSlice(res as Array<any>);
      }
      options.value = resList;
    })
    .finally(() => {
      loading.value = false;
    });
};

watch(
  () => modelValue.value,
  (val) => {
    if (val) {
      nextTick(() => {
        if (props.immediateFetch && props.valueField) {
          loading.value = true;
          const params: Record<string, any> = {
            [props.valueField]: val,
          };
          if (props.hasPagination) {
            params["pageNo"] = 1;
            params["pageSize"] = props.pageSize;
          }
          props
            /* .remoteMethods(params) */
            .remoteMethods()
            .then((res) => {
              let resList: Array<any> = [];
              if (!props.hasPagination) {
                resList = optionsSlice(res as Array<any>);
              } else {
                resList = optionsSlice(res.list);
              }
              options.value = resList;
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          // if (options.value.length > 0) return;
          // 避免编辑时无法绑定对象类型的问题
          options.value = [val];
        }
      });
    }
  }
);
</script>

<style scoped></style>
