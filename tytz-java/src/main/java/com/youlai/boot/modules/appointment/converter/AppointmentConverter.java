package com.youlai.boot.modules.appointment.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.appointment.model.entity.Appointment;
import com.youlai.boot.modules.appointment.model.form.AppointmentForm;
import com.youlai.boot.modules.appointment.model.vo.AppointmentVO;
import org.mapstruct.Mapper;

/**
 * 约见管理对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Mapper(componentModel = "spring")
public interface AppointmentConverter {

    /**
     * 实体转换为视图对象
     *
     * @param entity 实体
     * @return 视图对象
     */
    AppointmentVO entity2Vo(Appointment entity);

    /**
     * 实体分页转换为视图对象分页
     *
     * @param page 实体分页
     * @return 视图对象分页
     */
    Page<AppointmentVO> entity2Page(Page<Appointment> page);

    /**
     * 实体转换为表单对象
     *
     * @param entity 实体
     * @return 表单对象
     */
    AppointmentForm entity2Form(Appointment entity);

    /**
     * 表单对象转换为实体
     *
     * @param form 表单对象
     * @return 实体
     */
    Appointment form2Entity(AppointmentForm form);
} 