<script setup lang="ts">
import { ElDialog, type FormInstance } from "element-plus";
import { reactive, useTemplateRef, watch } from "vue";

import { DisplayableItem } from "./type";

type DisplayableDialogProps = {
  title: string;
  width: string;
  visible: boolean;
  displayData: any;
  displayItems: DisplayableItem[];
};

const props = withDefaults(defineProps<DisplayableDialogProps>(), {
  title: "详情",
  width: "700px",
  visible: false,
  displayData: null,
});

const emit = defineEmits<{
  "update:visible": [visible: boolean];
  refresh: [];
  close: [];
}>();

const ElFormRef = useTemplateRef<FormInstance>("ElFormRef");
const displayDataInternal = reactive<Record<string, any>>({});

// 监听visible变化，获取详情
watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      displayDataInternal.value = props.displayData;
      console.log("displayItems", displayDataInternal.value);
    }
  }
);

function closeDialog() {
  reset();
  emit("update:visible", false);
  emit("close");
}

const getStatusColor = (status: any) => {
  const colorMap = {
    PUSH: "#67c23a",
    WAIT: "#e6a23c",
    default: "#F56C6C",
  };
  return colorMap[status as keyof typeof colorMap] || colorMap.default;
};
const getStatusText = (status: any) => {
  const textMap = {
    PUSH: "已发布",
    WAIT: "待发布",
    default: "取消发布",
  };
  return textMap[status as keyof typeof textMap] || textMap.default;
};

const hanleDownLoad = (url: string) => {
  window.open(url);
};
function reset() {
  ElFormRef.value?.resetFields();
}
</script>

<template>
  <ElDialog :model-value="visible" :title="title" :width="width" @update:model-value="closeDialog">
    <div class="form-box">
      <div
        v-for="item in displayItems"
        :key="item.label"
        class="form-box-item"
        :style="item.width ? { width: item.width, padding: '10px' } : { padding: '10px' }"
      >
        <el-form-item class="w-full" v-bind="item">
          <span v-if="item.type === 'images'" style="display: flex; gap: 10px">
            <el-image
              v-for="(image, index) in Array.isArray(displayDataInternal.value[item.prop])
                ? displayDataInternal.value[item.prop]
                : [displayDataInternal.value[item.prop]]"
              :key="image.url"
              :zoom-rate="1.2"
              :max-scale="2.5"
              :min-scale="0.25"
              :src="image.url"
              style="height: 100px"
              :preview-src-list="
                (Array.isArray(displayDataInternal.value[item.prop])
                  ? displayDataInternal.value[item.prop]
                  : [displayDataInternal.value[item.prop]]
                ).map((image: any) => image.url)
              "
              :initial-index="index"
              fit="cover"
            />
          </span>
          <span v-else-if="item.type === 'documents'">
            <div
              v-for="doc in Array.isArray(displayDataInternal.value[item.prop])
                ? displayDataInternal.value[item.prop]
                : []"
              :key="doc.url"
              class="flex"
            >
              <div style="margin: 2px">
                <el-icon size="15"><Document /></el-icon>
              </div>
              <span class="color-#4080FF cursor-pointer" @click="hanleDownLoad(doc.url)">
                {{ doc.name }}
              </span>
            </div>
            <span v-if="displayDataInternal.value[item.prop] == ''">-</span>
          </span>
          <span v-else-if="item.type === 'rich-text'">
            <div style="width: 95%" v-html="displayDataInternal.value[item.prop]"></div>
          </span>
          <div v-else-if="item.type === 'radio'">
            <span
              :style="{
                color: getStatusColor(displayDataInternal.value[item.prop]),
              }"
            >
              {{ getStatusText(displayDataInternal.value[item.prop]) }}
            </span>
          </div>
          <span v-else-if="item.type === 'date-range'">
            {{ displayDataInternal.value[item.prop][0] }} 至
            {{ displayDataInternal.value[item.prop][1] }}
          </span>
          <span v-else>
            <div>
              <span v-if="displayDataInternal.value[item.prop]">
                {{ displayDataInternal.value[item.prop] }}
              </span>
              <span v-else class="placeholder">{{ item.placeholder }}</span>
            </div>
          </span>
        </el-form-item>
      </div>
    </div>
    <template #footer>
      <el-button @click="closeDialog">关闭</el-button>
    </template>
  </ElDialog>
</template>

<style scoped>
.placeholder {
  color: #eeeeee;
}
:deep(.el-descriptions__label) {
  width: 180px;
}
:deep(img) {
  width: 630px;
}

.el-form-item {
  margin-right: 0px !important;
  margin-bottom: 0px !important;
}
.form-box {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: space-between;
}
.footer {
  margin-left: auto;
}
</style>
