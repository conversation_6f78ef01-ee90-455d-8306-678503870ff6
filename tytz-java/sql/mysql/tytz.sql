/*
 Navicat Premium Dump SQL

 Source Server         : 本地测试库
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : localhost:13306
 Source Schema         : tytz

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 14/03/2025 14:42:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tsz_activity
-- ----------------------------
DROP TABLE IF EXISTS `tsz_activity`;
CREATE TABLE `tsz_activity`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '活动名称',
  `activity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '活动类型',
  `department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主办单位',
  `participants` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参与人员JSON数据',
  `start_time` datetime NULL DEFAULT NULL COMMENT '活动开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '活动结束时间',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动内容',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '附件JSON数据，包含文件名、路径等信息',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '活动信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_appointment
-- ----------------------------
DROP TABLE IF EXISTS `tsz_appointment`;
CREATE TABLE `tsz_appointment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `appointment_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '约见人名称',
  `appointment_unit` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属单位',
  `appointment_contact` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系方式',
  `appointment_department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '被约见部委',
  `appointment_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '约见原因',
  `appointment_start_time` datetime NULL DEFAULT NULL COMMENT '约见开始时间',
  `appointment_end_time` datetime NULL DEFAULT NULL COMMENT '约见结束时间',
  `handle_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理状态（1: 予以约见, 2: 不予约见）',
  `handle_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '处理意见',
  `feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '约见反馈',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '约见管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_business_materials
-- ----------------------------
DROP TABLE IF EXISTS `tsz_business_materials`;
CREATE TABLE `tsz_business_materials`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '材料名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '材料内容',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '附件JSON数据，包含文件名、路径等信息',
  `submit_by` bigint NOT NULL COMMENT '提交人ID',
  `active_start_time` datetime NOT NULL COMMENT '活动开始时间',
  `active_end_time` datetime NOT NULL COMMENT '活动结束时间',
  `create_by` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '营商材料表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_meeting
-- ----------------------------
DROP TABLE IF EXISTS `tsz_meeting`;
CREATE TABLE `tsz_meeting`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会议名称',
  `meeting_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会议类型',
  `department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组织者',
  `participants` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参会人员JSON数据',
  `start_time` datetime NULL DEFAULT NULL COMMENT '会议开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '会议结束时间',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '会议内容',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '附件JSON数据，包含文件名、路径等信息',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会议信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_news
-- ----------------------------
DROP TABLE IF EXISTS `tsz_news`;
CREATE TABLE `tsz_news`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '新闻标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新闻内容',
  `publish_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '发布状态（0: 未发布, 1: 已发布, 2: 待发布）',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '附件JSON数据，包含文件名、路径等信息',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '新闻资讯表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_notice
-- ----------------------------
DROP TABLE IF EXISTS `tsz_notice`;
CREATE TABLE `tsz_notice`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '通知内容',
  `publish_status` tinyint NULL DEFAULT 0 COMMENT '发布状态（0: 未发布, 1: 已发布, 2: 待发布）',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '附件JSON数据，包含文件名、路径等信息',
  `create_by` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_opinion
-- ----------------------------
DROP TABLE IF EXISTS `tsz_opinion`;
CREATE TABLE `tsz_opinion`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '营商环境问题内容',
  `member_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商会会员名称',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属部门',
  `contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系方式',
  `create_by` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '意见征集表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_problem
-- ----------------------------
DROP TABLE IF EXISTS `tsz_problem`;
CREATE TABLE `tsz_problem`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '营商环境标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '营商环境问题内容',
  `business_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '营商环境类别',
  `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
  `member_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商会会员名称',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属部门',
  `adopt_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采纳状态',
  `adopt_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '采纳意见',
  `adopt_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采纳人',
  `adopt_time` datetime NULL DEFAULT NULL COMMENT '采纳时间',
  `instruction_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '批示状态',
  `instruction_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '批示内容',
  `instruction_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '批示人',
  `instruction_time` datetime NULL DEFAULT NULL COMMENT '批示时间',
  `leader_instruction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '领导批示',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '营商环境问题表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tsz_welfare
-- ----------------------------
DROP TABLE IF EXISTS `tsz_welfare`;
CREATE TABLE `tsz_welfare`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公益活动名称',
  `department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主办单位',
  `participants` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参与人员JSON数据',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动内容',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '附件JSON数据，包含文件名、路径等信息',
  `create_by` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '公益信息表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for tsz_scoring_record
-- ----------------------------
DROP TABLE IF EXISTS `tsz_scoring_record`;
CREATE TABLE `tsz_scoring_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `member_name` varchar(255) DEFAULT NULL COMMENT '商会会员名称',
  `member_id` bigint DEFAULT NULL COMMENT '商会会员id',
  `department` varchar(255) DEFAULT NULL COMMENT '所属部门',
  `scoring_type` varchar(255) DEFAULT NULL COMMENT '评分类型',
  `scoring_detail` varchar(255) DEFAULT NULL COMMENT '评分详细内容',
  `score` varchar(255) DEFAULT NULL COMMENT '得分',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `meeting_id` bigint DEFAULT NULL COMMENT '会议id',
  `work_id` bigint DEFAULT NULL COMMENT '重点工作id',
  `activity_id` bigint DEFAULT NULL COMMENT '活动id',
  `problem_id` bigint DEFAULT NULL COMMENT '问题id',
  `year` varchar(255) DEFAULT NULL COMMENT '年度',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- 添加营商环境管理系统菜单数据
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
	(200, 0, '0', '营商环境管理', 2, '/biz_env', '/biz_env', 'Layout', NULL, 0, 0, 1, 0, 'menu', '/issue_mgmt/index', NOW(), NOW(), NULL),
	(201, 200, '0,200', '问题审核', 1, 'Issue', 'issue', 'issue_mgmt/index', NULL, 0, 1, 1, 1, 'code', NULL, NOW(), NOW(), NULL),
	(202, 200, '0,200', '领导批示', 1, 'Approval', 'approval', 'approval_mgmt/index', NULL, 0, 1, 1, 2, 'role', NULL, NOW(), NOW(), NULL),
	(210, 0, '0', '意见采集管理', 2, '/feedback', '/feedback', 'Layout', NULL, 0, 0, 1, 1, 'menu', NULL, NOW(), NOW(), NULL),
	(211, 210, '0,210', '意见征集汇总', 1, 'Feedback', 'feedback', 'feedback_mgmt/index', NULL, 0, 1, 1, 1, 'code', NULL, NOW(), NOW(), NULL),
	(220, 0, '0', '约见信息管理', 2, '/appoint', '/appoint', 'Layout', NULL, 0, 0, 1, 2, 'menu', '/appointment_mgmt/index', NOW(), NOW(), NULL),
	(221, 220, '0,220', '约见办理', 1, 'Appoint', 'appoint', 'appointment_mgmt/index', NULL, 0, 1, 1, 1, 'code', NULL, NOW(), NOW(), NULL),
	(222, 220, '0,220', '约见信息反馈', 1, 'appoFeedback', 'appo_feedback', 'appointment_mgmt/feedback', NULL, 0, 1, 1, 2, 'role', NULL, NOW(), NOW(), NULL),
	(230, 0, '0', '新闻资讯管理', 2, '/news', '/news', 'Layout', NULL, 0, 0, 1, 3, 'menu', '/news_mgmt/news', NOW(), NOW(), NULL),
	(231, 230, '0,230', '新闻资讯', 1, 'News', 'news', 'news_mgmt/news', NULL, 0, 1, 1, 1, 'code', NULL, NOW(), NOW(), NULL),
	(232, 230, '0,230', '通知公告', 1, 'Notice', 'notice', 'news_mgmt/notice', NULL, 0, 1, 1, 2, 'role', NULL, NOW(), NOW(), NULL),
	(240, 0, '0', '评分信息管理', 2, '/rating', '/rating', 'Layout', NULL, 0, 0, 1, 4, 'menu', '/rating_mgmt/activity', NOW(), NOW(), NULL),
	(241, 240, '0,240', '活动信息', 1, 'Activity', 'activity', 'rating_mgmt/activity', NULL, 0, 1, 1, 1, 'code', NULL, NOW(), NOW(), NULL),
	(242, 240, '0,240', '会议信息', 1, 'Meeting', 'meeting', 'rating_mgmt/meeting', NULL, 0, 1, 1, 2, 'role', NULL, NOW(), NOW(), NULL),
	(243, 240, '0,240', '年度重点工作', 1, 'AnnualKeyTasks', 'annual_key_tasks', 'rating_mgmt/annual_key_tasks', NULL, 0, 1, 1, 3, 'role', NULL, NOW(), NOW(), NULL),
	(250, 0, '0', '委员履职评分', 2, '/member_rating', '/member_rating', 'Layout', NULL, 0, 0, 1, 5, 'menu', '/member_rating_mgmt/activity', NOW(), NOW(), NULL),
	(251, 250, '0,250', '评分规则', 1, 'ScoringRules', 'scoringRules', 'member_rating_mgmt/scoringRules', NULL, 0, 1, 1, 1, 'code', NULL, NOW(), NOW(), NULL),
	(252, 250, '0,250', '评价表管理', 1, 'Evaluation', 'evaluation', 'member_rating_mgmt/evaluation', NULL, 0, 1, 1, 2, 'role', NULL, NOW(), NOW(), NULL);
