package com.youlai.boot.modules.welfare.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 公益信息分页查询对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Schema(description ="公益信息查询对象")
@Getter
@Setter
public class WelfareQuery extends BasePageQuery {
    
    @Schema(description = "公益活动名称")
    private String title;
    
    @Schema(description = "主办单位")
    private String department;
    
    @Schema(description = "开始时间起始值")
    private LocalDateTime startTimeBegin;
    
    @Schema(description = "开始时间结束值")
    private LocalDateTime startTimeEnd;
} 