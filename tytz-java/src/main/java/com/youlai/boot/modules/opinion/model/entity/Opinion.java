package com.youlai.boot.modules.opinion.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import lombok.Data;

/**
 * 意见征集实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@TableName("tsz_opinion")
@Data
public class Opinion extends BaseEntity {

    /**
     * 意见内容
     */
    private String content;
    
    /**
     * 商会会员名称
     */
    private String memberName;

    /**
     * 所属单位
     */
    private String department;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 创建人ID
     */
    private Long createBy;
    
    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;

    /**
     * 商会会员id
     */
    private Long memberId;

    /**
     * 会员身份
     */
    private String memberRole;
} 