import { ElMessage, ElMessageBox } from "element-plus";

import { useUserStoreHook } from "@/store/modules/user";

import type { InsertFnType } from "./type";
//import request from "@/utils/request";

/* function customCheckImageFn(src: string, alt: string, url: string): boolean | undefined | string {
  // TS 语法
  // function customCheckImageFn(src, alt, url) {                                                    // JS 语法
  if (!src) {
    return;
  }
  if (src.indexOf("http") !== 0) {
    return "图片网址必须以 http/https 开头";
  }
  return true;

  // 返回值有三种选择：
  // 1. 返回 true ，说明检查通过，编辑器将正常插入图片
  // 2. 返回一个字符串，说明检查未通过，编辑器会阻止插入。会 alert 出错误信息（即返回的字符串）
  // 3. 返回 undefined（即没有任何返回），说明检查未通过，编辑器会阻止插入。但不会提示任何信息
} */

// 转换图片链接
/* function customParseImageSrc(src: string): string {
  // TS 语法
  // function customParseImageSrc(src) {               // JS 语法
  if (src.indexOf("http") !== 0) {
    return `http://${src}`;
  }
  return src;
} */

export const customInsert = function (res: any, insertFn: InsertFnType) {
  return richTextUploadInterceptor(res)
    .then((data) => {
      const imageUrl = data.data.url.replace(/\\/g, "/"); // 替换所有 \ 为 /
      insertFn(imageUrl, "", "");
      return Promise.resolve(data);
    })
    .catch((err) => {
      ElMessage.success({
        message: err,
        grouping: true,
      });
    });
};
// 上传之前触发
export const onBeforeUpload = (file: File) => {
  return file;

  // 可以 return
  // 1. return file 或者 new 一个 file ，接下来将上传
  // 2. return false ，不上传这个 file
};

/**
 * 图片插入到编辑器后触发 配置=> editorConfig.MENU_CONF.insertImage
 * @type src 为base64
 * @param imageNode
 * @param cb
 */
export const onInsertedImage = (imageNode: any | null, cb: (file: File) => void) => {
  if (!imageNode) return;
  const { src, alt, url, href } = imageNode;
  const file = base64ToFile(src, alt);
  console.log(
    "inserted image",
    `file==>`,
    file,
    `src==>${src}`,
    `alt==>${alt}`,
    `url==>${url}`,
    `href==>${href}`
  );
  cb(file);
  return file;
};
/**
 * 将base64转换为blob
 * @param dataurl
 * @param fileName
 */
const base64ToFile = (dataurl: string, fileName: string = "image") => {
  const arr = dataurl.split(","),
    matchResult = arr[0].match(/:(.*?);/),
    mime = matchResult ? matchResult[1] : "",
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  for (let i = 0; i < n; i++) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  const theBlob = new Blob([u8arr], { type: mime });
  Object.assign(theBlob, {
    lastModified: new Date().getTime(),
    name: fileName,
  });
  return new File([theBlob], fileName, { type: mime });
};
// 编辑图片
export const onUpdatedImage = (imageNode: any | null) => {
  // TS 语法
  // onUpdatedImage(imageNode) {                    // JS 语法
  if (imageNode == null) return;

  const { src, alt, url } = imageNode;
  console.log("updated image", src, alt, url);
};

/**
 * 富文本图片上传拦截器
 * @param response
 */
export const richTextUploadInterceptor = (response: any) => {
  ElMessageBox.close();
  const res = response;
  const reLoginCode = ["50009", "500010", "401", "50008", "50012", "500011", "50014", "5001001"];
  const responseCode = response?.code || response?.data?.data?.code || response.status;
  if (response.status === 501 || reLoginCode.includes(`${responseCode}`)) {
    ElMessageBox.confirm("登录信息已过期，请重新打开该页面")
      .then(() => {
        useUserStoreHook().logout();
        window.location.href = "/login";
      })
      .catch(() => {
        useUserStoreHook().logout();
        window.location.href = "/login";
      });

    return Promise.reject(res.msg || "Error");
  }
  if (res.code === 0 || res.code === "0") {
    if (res.data?.code === "500011") {
      ElMessageBox.confirm(res.data.msg, "登录失败", {
        confirmButtonText: "重新登录",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          useUserStoreHook().logout();
          window.location.href = "/login";
        })
        .catch(() => {
          useUserStoreHook().logout();
          window.location.href = "/login";
        });
    }
    return Promise.resolve(res.data);
  } else if (res.code !== "00000") {
    ElMessage.error({
      message: res.msg || "系统发生异常，请联系管理员。",
      grouping: true,
    });
    return Promise.reject(res.msg || "系统发生异常，请联系管理员。");
  }

  return Promise.resolve(res);
};
