import request from '@/utils/request'

/**
 * DataOpinionVO
 */
export interface DataOpinionVO {
  list?: OpinionVO[]
  total?: number
}

/**
 * 意见征集视图对象
 *
 * OpinionVO
 */
export interface OpinionVO {
  /**
   * 联系方式
   */
  contact?: string
  /**
   * 意见内容
   */
  content?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 所属单位
   */
  department?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 商会会员名称
   */
  memberName?: string
  [property: string]: any
}

/**
 * 意见收集
 * @param data
 */
export const collectOpinion = (data: Record<string, any>) => {
  return request('/api/v1/opinions', {
    method: 'POST',
    data,
  })
}
