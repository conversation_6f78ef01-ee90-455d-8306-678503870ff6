export const calculateDaysAgo = (date: string) => {
  const today = new Date()
  const targetDate = new Date(date)
  const timeDiff = Math.abs(today.getTime() - targetDate.getTime())
  const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24))
  const diffHours = Math.ceil(timeDiff / (1000 * 3600))
  const diffMinutes = Math.ceil(timeDiff / (1000 * 60))
  const diffSeconds = Math.ceil(timeDiff / 1000)
  if (diffDays > 0) return `${diffDays}天前`
  if (diffHours > 0) return `${diffHours}小时前`
  if (diffMinutes > 0) return `${diffMinutes}分钟前`
  if (diffSeconds > 0) return `${diffSeconds}秒前`
  return '刚刚'
}
