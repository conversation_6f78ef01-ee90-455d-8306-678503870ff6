package com.youlai.boot.modules.businessmaterials.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youlai.boot.modules.businessmaterials.model.entity.BusinessMaterials;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.businessmaterials.model.query.BusinessMaterialsQuery;
import com.youlai.boot.modules.businessmaterials.model.vo.BusinessMaterialsVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 营商材料Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper
public interface BusinessMaterialsMapper extends BaseMapper<BusinessMaterials> {

    /**
     * 获取营商材料分页数据
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return
     */
    Page<BusinessMaterialsVO> getBusinessMaterialsPage(Page<BusinessMaterialsVO> page, BusinessMaterialsQuery queryParams);

}
