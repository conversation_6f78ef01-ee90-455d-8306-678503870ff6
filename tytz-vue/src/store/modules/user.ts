import { store } from "@/store";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useDictStoreHook } from "@/store/modules/dict";

import AuthAP<PERSON>, { type LoginFormData } from "@/api/auth";
import User<PERSON><PERSON>, { type UserInfo } from "@/api/system/user";

import { setAccessToken, setRefreshToken, getRefreshToken, clearToken } from "@/utils/auth";
import { getAppointmentListApi } from "@/api/appointment/index";
import { statusStyleEnum } from "@/views/appointment_mgmt/enum";

export const useUserStore = defineStore("user", () => {
  const userInfo = useStorage<UserInfo>("userInfo", {} as UserInfo);

  const appointmentCount = ref(0);
  const appointmentTimer = ref<ReturnType<typeof setInterval> | null>(null);

  /**
   * 登录
   *
   * @param {LoginFormData}
   * @returns
   */
  function login(LoginFormData: LoginFormData) {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.login(LoginFormData)
        .then((data) => {
          const { accessToken, refreshToken } = data;
          setAccessToken(accessToken); // eyJhbGciOiJIUzI1NiJ9.xxx.xxx
          setRefreshToken(refreshToken);
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  /**
   * 获取用户信息
   *
   * @returns {UserInfo} 用户信息
   */
  function getUserInfo() {
    return new Promise<UserInfo>((resolve, reject) => {
      UserAPI.getInfo()
        .then((data) => {
          if (!data) {
            reject("Verification failed, please Login again.");
            return;
          }
          Object.assign(userInfo.value, { ...data });
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 登出
   */
  function logout() {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.logout()
        .then(() => {
          clearUserData();
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 刷新 token
   */
  function refreshToken() {
    const refreshToken = getRefreshToken();
    return new Promise<void>((resolve, reject) => {
      AuthAPI.refreshToken(refreshToken)
        .then((data) => {
          const { accessToken, refreshToken } = data;
          setAccessToken(accessToken);
          setRefreshToken(refreshToken);
          resolve();
        })
        .catch((error) => {
          console.log(" refreshToken  刷新失败", error);
          reject(error);
        });
    });
  }

  /**
   * 清理用户数据
   *
   * @returns
   */
  function clearUserData() {
    return new Promise<void>((resolve) => {
      clearToken();
      usePermissionStoreHook().resetRouter();
      useDictStoreHook().clearDictionaryCache();
      resolve();
    });
  }

  /***侧边栏获取未约见信息条数 */
  async function fetchAppointmentCount() {
    try {
      const count = await getAppointmentListApi(statusStyleEnum.WAIT.value);
      appointmentCount.value = count?.length || 0;
    } catch (error) {
      console.error("获取约见信息条数失败:", error);
    }
  }

  function startAppointmentTimer() {
    if (!appointmentTimer.value) {
      fetchAppointmentCount();
      appointmentTimer.value = setInterval(() => {
        fetchAppointmentCount();
      }, 30000);
    }
  }

  function stopAppointmentTimer() {
    if (appointmentTimer.value) {
      clearInterval(appointmentTimer.value);
      appointmentTimer.value = null;
    }
  }

  return {
    userInfo,
    getUserInfo,
    login,
    logout,
    clearUserData,
    refreshToken,
    appointmentCount,
    fetchAppointmentCount,
    startAppointmentTimer,
    stopAppointmentTimer,
  };
});

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useUserStoreHook() {
  return useUserStore(store);
}
