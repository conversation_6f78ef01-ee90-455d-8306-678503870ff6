package com.youlai.boot.modules.appointment.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.youlai.boot.common.enums.AppoinitMentEnum;
import com.youlai.boot.modules.appointment.converter.AppoinitMentEnumConverter;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AppointmentExcelVO {
    
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private Long index;
    
    @ExcelProperty(value = "约见人", index = 1)
    @ColumnWidth(15)
    private String appointmentName;
    
    @ExcelProperty(value = "所属单位", index = 2)
    @ColumnWidth(20)
    private String appointmentUnit;
    
    @ExcelProperty(value = "被约见部委", index = 3)
    @ColumnWidth(20)
    private String appointmentDepartment;
    
    @ExcelProperty(value = "约见开始时间", index = 4)
    @ColumnWidth(20)
    private LocalDateTime appointmentStartTime;
    
    @ExcelProperty(value = "约见结束时间", index = 5)
    @ColumnWidth(20)
    private LocalDateTime appointmentEndTime;
    
    @ExcelProperty(value = "约见状态", index = 6, converter = AppoinitMentEnumConverter.class)
    @ColumnWidth(15)
    private AppoinitMentEnum appointmentStatus;
    
    // 根据实际业务需求添加更多字段
} 