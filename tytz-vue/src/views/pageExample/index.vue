<script setup lang="ts">
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";

const ExampleEntityCreditLevelEnum = {
  A: "A 优秀",
  B: "B 良好",
  C: "C 一般",
  D: "D 较差",
};

type ExampleEntity = {
  id?: string;
  name: string;
  address: string;
  establishYear: string;
  creditLevel: keyof typeof ExampleEntityCreditLevelEnum;
  legalPerson: string;
  phone: string;
  introduction: string;
  documents?: string[];
  pictures?: string[];
  createTime: string;
  isPublished: boolean;
  isApproved: boolean;
};

const config: EntityCrudProps<ExampleEntity> = {
  entityName: "example",
  displayName: "示例",
  filterFormItems: {
    name: {
      type: "input",
      label: "名称",
    },
    creditLevel: {
      type: "select",
      label: "信用等级",
      options: getEnumOptions(ExampleEntityCreditLevelEnum),
    },
    createTime: {
      type: "date-range",
      label: "创建时间",
      // 选择时间范围时后端通常会给出两个字段，这时需要指定 dateRangeFields 属性来对应后端的两个字段
      dateRangeFields: ["createTimeStart", "createTimeEnd"],
    },
  },
  operations: [
    // 如果需要覆盖原来的内部操作，则需要使用 overwriteInternalType 属性
    // 如果只是需要更改按钮的文本，只需要使用 createButtonLabel 属性
    {
      overwriteInternalType: "create",
      type: "button",
      label: "新增",
      colorize: "warning",
      actionService: (params, internalService) => {
        console.log("create", params);
        internalService?.(params);
        return Promise.resolve();
      },
    },
    {
      type: "button",
      label: "导出",
      colorize: "primary",
      actionService: () => Promise.resolve(),
    },
  ],
  hasIndexColumn: true,
  hasSelectionColumn: true,
  tableColumns: {
    name: "名称",
    address: "地址",
    establishYear: "成立年限",
    creditLevel: "信用等级",
    isPublished: {
      label: "发布状态",
      formatter: (row) => (row.isPublished ? "已发布" : "未发布"),
    },
    isApproved: {
      label: "审核状态",
      formatter: (row) => (row.isApproved ? "已通过" : "未通过"),
    },
    // 如果需要指定操作列的表头名或宽度，可以显式声明 operations 列
    operations: {
      label: "操作",
      width: "400",
    },
  },
  rowOperations: [
    // 覆盖原来的内部操作
    {
      overwriteInternalType: "detail",
      type: "link",
      label: "查看",
      actionService: (row, internalService) => {
        console.log("detail", row);
        internalService?.(row);
        return Promise.resolve();
      },
    },
    {
      type: "link",
      label: "废弃",
      colorize: () => "danger",
      actionService: () => Promise.resolve(),
    },
  ],
  listFetchService: (params) => {
    console.log("listFetchService params", params);
    //return mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          list: [
            {
              id: "1",
              name: "示例1",
              address: "地址1",
              establishYear: "2020",
              creditLevel: "A",
              legalPerson: "张三",
              phone: "1234567890",
              introduction: "介绍1",
              createTime: "2020-01-01",
              isPublished: true,
              isApproved: true,
            },
            {
              id: "2",
              name: "示例2",
              address: "地址2",
              establishYear: "2020",
              creditLevel: "B",
              legalPerson: "李四",
              phone: "1234567890",
              introduction: "介绍2",
              createTime: "2020-01-01",
              isPublished: true,
              isApproved: true,
            },
          ],
          total: 0,
        });
      }, 500);
    });
  },
  createFormItems: {
    name: {
      type: "input",
      label: "名称",
      required: true,
      width: "50%",
    },
    address: {
      type: "input",
      label: "地址",
      width: "50%",
    },
    establishYear: {
      type: "input",
      label: "成立年限",
    },
    creditLevel: {
      type: "select",
      label: "信用等级",
      options: getEnumOptions(ExampleEntityCreditLevelEnum),
    },
    legalPerson: {
      type: "person-name",
      label: "法人",
    },
    phone: {
      type: "mobile",
      label: "电话",
    },
    introduction: {
      type: "editor",
      label: "介绍",
    },
    documents: {
      type: "multiple-document",
      label: "附件",
    },
    pictures: {
      type: "multiple-image",
      label: "照片",
    },
    isApproved: {
      type: "switch",
      label: "审核状态",
    },
  },
  // createButtonLabel 属性可以更改创建按钮的文本
  // createButtonLabel: '新增示例',
  createService: (newRecord: ExampleEntity) => Promise.resolve(newRecord),

  useCreateFormItemsAsUpdate: true,
  fetchUpdateableDataService: (record: ExampleEntity) => {
    // 可以在这里处理一些逻辑，比如从后端接口获取完整的修改值
    return Promise.resolve({ ...record, name: "示例_fetchUpdateableDataService" });
  },
  updateService: (updateRecord: ExampleEntity) => Promise.resolve(updateRecord),

  useCreateFormItemsAsDetail: true,
  detailFetchService: (record: ExampleEntity) => Promise.resolve(record),

  deleteService: (record: ExampleEntity) => Promise.resolve(record),
  batchDeleteService: (records: ExampleEntity[]) => Promise.resolve(records),

  canPublish: (record: ExampleEntity) => !record.isPublished,
  publishService: (record: ExampleEntity) => Promise.resolve(record),

  canUnpublish: (record: ExampleEntity) => record.isPublished,
  unpublishService: (record: ExampleEntity) => Promise.resolve(record),

  canApprove: (record: ExampleEntity) => !record.isApproved,
  approveService: (record: ExampleEntity) => Promise.resolve(record),

  canReject: (record: ExampleEntity) => record.isApproved,
  rejectService: (record: ExampleEntity) => Promise.resolve(record),
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
  <div>
    <EntityCrud v-bind="entityCrudProps">
      <template #tableCreditLevel="{ row }: { row: ExampleEntity }">
        <el-tag v-if="row.creditLevel === 'A'" type="primary">A</el-tag>
        <el-tag v-else type="danger">B</el-tag>
      </template>
    </EntityCrud>
  </div>
</template>
