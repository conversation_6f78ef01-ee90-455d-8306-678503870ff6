<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, ref, useSlots } from "vue";

import EditableForm from "@/components/EditableForm/index.vue";
import DisplayableDialog from "@/components/EntityCrud/DisplayableDialog/index.vue";
import SearchPage from "@/components/SearchPage/index.vue";

import {
  DEFAULT_APPROVE_BUTTON_LABEL,
  DEFAULT_BATCH_DELETE_BUTTON_LABEL,
  DEFAULT_CREATE_BUTTON_LABEL,
  DEFAULT_DELETE_BUTTON_LABEL,
  DEFAULT_DETAIL_BUTTON_LABEL,
  DEFAULT_PUBLISH_BUTTON_LABEL,
  DEFAULT_REJECT_BUTTON_LABEL,
  DEFAULT_TOP_BUTTON_LABEL,
  DEFAULT_UNPUBLISH_BUTTON_LABEL,
  DEFAULT_UNTOP_BUTTON_LABEL,
  DEFAULT_UPDATE_BUTTON_LABEL,
} from "./const";
import { useDisplayableDialog } from "./DisplayableDialog/hook";
import { useCreatableDialog, useEditableDialog } from "./EditableDialog/hook";
import { useSearchableTable } from "./SearchableTable/hook";
import { EntityCrudProps, InternalOperationType, RowOperation } from "./type";
import { mapEntityFormItemToDisplayableItem } from "./util";

const searchPageRef = ref<any>(null);

const props = defineProps<EntityCrudProps<any>>();

const canCreate = computed(() => !!props.createService);
const canUpdate = computed(() => !!props.updateService);
const canDetail = computed(() => !!props.detailFetchService);
const canDelete = computed(() => !!props.deleteService);
const canBatchDelete = computed(() => !!props.batchDeleteService);
const canPublish = computed(() => !!props.publishService);
const canUnpublish = computed(() => !!props.unpublishService);
const canApprove = computed(() => !!props.approveService);
const canReject = computed(() => !!props.rejectService);
const canTop = computed(() => !!props.topService);
const canUntop = computed(() => !!props.untopService);

const selectedRows = ref<any[]>([]);

const handleWithConfirmWithMessage = async (param: {
  message: string;
  promise: () => Promise<any>;
  successMessage?: string;
  errorMessage?: string;
  dontRefresh?: boolean;
}) => {
  try {
    await ElMessageBox.confirm(param.message, "提示", {
      type: "warning",
      confirmButtonText: "确认",
      cancelButtonText: "取消",
    });
  } catch (error) {
    return error;
  }
  try {
    if (param.promise) {
      await param.promise();
      ElMessage.success(param.successMessage ?? "操作成功");
    }
    if (!param.dontRefresh) {
      searchPageRef.value.refresh?.();
    }
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error(param.errorMessage ?? "操作失败");
  }
};

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

let createFormProps: any, updateFormProps: any, detailItemProps: any;
let newEditable: any, editEditable: any, showDisplayable: any;

if (canCreate.value) {
  ({ formProps: createFormProps, showDialog: newEditable } = useCreatableDialog({
    title: props.createButtonLabel ?? DEFAULT_CREATE_BUTTON_LABEL,
    formItems: props.createFormItems ?? {},
    submitRequest: (data) => props.createService?.(data) ?? Promise.resolve(),
    width: "800px",
    onSuccess: () => {
      searchPageRef.value?.refresh?.();
    },
  }));
}

if (canUpdate.value) {
  const formItems = props.useCreateFormItemsAsUpdate
    ? props.createFormItems
    : props.updateFormItems;

  ({ formProps: updateFormProps, showDialog: editEditable } = useEditableDialog({
    title: props.updateButtonLabel ?? DEFAULT_UPDATE_BUTTON_LABEL,
    formItems: formItems ?? {},
    initialDataService: props.fetchUpdateableDataService,
    submitRequest: (data) => props.updateService?.(data) ?? Promise.resolve(),
    width: "800px",
    onSuccess: () => {
      searchPageRef.value.refresh?.();
    },
  }));
}

if (canDetail.value) {
  const displayItems = props.useCreateFormItemsAsDetail
    ? props.createFormItems
    : props.detailFormItems;

  ({ detailItemProps, showDisplayable } = useDisplayableDialog({
    displayItems: Object.keys(displayItems || {})
      .filter((key) => {
        const item = displayItems?.[key];
        return typeof item !== "string" && item?.type !== "id" && item !== undefined;
      })
      .map((key) => {
        const item = displayItems?.[key];
        if (item === undefined || item === null) {
          console.warn(`Key "${key}" in displayItems is skipped because it is undefined or null.`);
          return null;
        }
        return mapEntityFormItemToDisplayableItem(key, item);
      })
      .filter((item) => item !== null && item !== undefined), // 过滤掉 null 和 undefined
  }));
}

// 合并内部操作
const mergeInternalOperation = (
  internalType: InternalOperationType,
  type: "button" | "link",
  label: string,
  colorize: string,
  internalActionService: (_params: any) => Promise<void>
) => {
  //判断 props.operations 有没有对应 internalType 的 operation，如果有则返回 operation，否则返回新的 operation
  const operation = props.operations?.find(
    (operation) =>
      operation.overwriteInternalType && operation.overwriteInternalType === internalType
  ) ?? {
    type,
    label,
    colorize,
    overwriteInternalType: null, // 默认不是内部操作
    actionService: null, // 默认没有 actionService
  };
  return {
    is: operation.type,
    props: {
      label: operation.label,
      type: operation.colorize as
        | "primary"
        | "success"
        | "warning"
        | "danger"
        | "info"
        | "text"
        | "default",
      onClick() {
        if (operation.overwriteInternalType) {
          return operation.actionService?.(selectedRows.value, internalActionService);
        } else {
          return internalActionService(selectedRows.value);
        }
      },
    },
    displayIndex: undefined,
  };
};

const mergeRowInternalOperation = (
  internalType: InternalOperationType,
  type: "button" | "link",
  label: string,
  colorize: string,
  internalActionService: (_row: any) => Promise<void>,
  canDisplay?: (_row: any) => boolean
): RowOperation<any> => {
  //判断 props.operations 有没有对应 internalType 的 operation，如果有则返回 operation，否则返回新的 operation
  const operation = props.rowOperations?.find(
    (operation) =>
      operation.overwriteInternalType && operation.overwriteInternalType === internalType
  ) ?? {
    type,
    label,
    colorize,
    overwriteInternalType: null, // 默认不是内部操作
    actionService: null, // 默认没有 actionService
  };
  return {
    type: operation.type,
    label: operation.label,
    colorize: () => colorize,
    actionService: (row) => {
      if (operation.overwriteInternalType) {
        return operation.actionService(row, internalActionService);
      } else {
        return internalActionService(row);
      }
    },
    canDisplay: canDisplay ?? (() => true),
  };
};

const searchPageProps = useSearchableTable({
  ...props,
  hasBatchService: !!props.batchDeleteService,
  hasExternalRowOperation:
    canUpdate.value || canApprove.value || canUnpublish.value || canReject.value || canDelete.value,
  tableOperations: [
    ...(canCreate.value
      ? [
          mergeInternalOperation(
            "create",
            "button",
            props.createButtonLabel ?? DEFAULT_CREATE_BUTTON_LABEL,
            "primary",
            newEditable
          ),
        ]
      : []),
    ...(canBatchDelete.value
      ? [
          mergeInternalOperation(
            "batchDelete",
            "button",
            props.batchDeleteButtonLabel ?? DEFAULT_BATCH_DELETE_BUTTON_LABEL,
            "danger",
            async (selectedRows): Promise<void> => {
              if (selectedRows.length === 0) {
                ElMessage.warning(
                  `请选择要${props.batchDeleteButtonLabel ?? DEFAULT_BATCH_DELETE_BUTTON_LABEL}的项`
                );
                return Promise.resolve();
              }
              await handleWithConfirmWithMessage({
                message: `确认${props.batchDeleteButtonLabel ?? DEFAULT_BATCH_DELETE_BUTTON_LABEL}吗？`,
                promise: () => props.batchDeleteService?.(selectedRows) ?? Promise.resolve(),
                successMessage: `${props.batchDeleteButtonLabel ?? DEFAULT_BATCH_DELETE_BUTTON_LABEL}成功`,
              });
            }
          ),
        ]
      : []),
    ...(props.operations
      ? props.operations
          .filter((operation) => !operation.overwriteInternalType)
          .map((operation) => ({
            is: "button",
            props: {
              label: operation.label,
              type: operation.colorize,
              onClick: operation.actionService,
            },
            displayIndex: operation.displayIndex,
          }))
      : []),
  ].sort((a, b) => (a.displayIndex ?? 0) - (b.displayIndex ?? 0)),
});

const rowOperations = computed(() =>
  [
    ...(canDetail.value
      ? [
          mergeRowInternalOperation(
            "detail",
            "link",
            props.detailButtonLabel ?? DEFAULT_DETAIL_BUTTON_LABEL,
            "primary",
            showDisplayable,
            props.canViewDetail ?? undefined
          ),
        ]
      : []),
    ...(canUpdate.value
      ? [
          mergeRowInternalOperation(
            "update",
            "link",
            props.updateButtonLabel ?? DEFAULT_UPDATE_BUTTON_LABEL,
            "primary",
            editEditable,
            props.canUpdate ?? undefined
          ),
        ]
      : []),
    ...(canPublish.value
      ? [
          mergeRowInternalOperation(
            "publish",
            "link",
            props.publishButtonLabel ?? DEFAULT_PUBLISH_BUTTON_LABEL,
            "primary",
            async (row) => {
              if (props.publishService) {
                await handleWithConfirmWithMessage({
                  message: `确认${props.publishButtonLabel ?? DEFAULT_PUBLISH_BUTTON_LABEL}吗？`,
                  promise: () => Promise.resolve(props?.publishService?.(row) || row),
                  successMessage: `${props.publishButtonLabel ?? DEFAULT_PUBLISH_BUTTON_LABEL}成功`,
                });
              }
            },
            (row) => (props.canPublish ? props.canPublish(row) : true)
          ),
        ]
      : []),
    ...(canUnpublish.value
      ? [
          mergeRowInternalOperation(
            "unpublish",
            "link",
            props.unpublishButtonLabel ?? DEFAULT_UNPUBLISH_BUTTON_LABEL,
            "primary",
            async (row) => {
              await handleWithConfirmWithMessage({
                message: `确认${props.unpublishButtonLabel ?? DEFAULT_UNPUBLISH_BUTTON_LABEL}吗？`,
                promise: () => Promise.resolve(props?.unpublishService?.(row) || row),
                successMessage: `${props.unpublishButtonLabel ?? DEFAULT_UNPUBLISH_BUTTON_LABEL}成功`,
              });
            },
            (row) => (props.canUnpublish ? props.canUnpublish(row) : true)
          ),
        ]
      : []),
    ...(canTop.value
      ? [
          mergeRowInternalOperation(
            "top",
            "link",
            props.topButtonLabel ?? DEFAULT_TOP_BUTTON_LABEL,
            "primary",
            async (row) => {
              await handleWithConfirmWithMessage({
                message: `确认${props.topButtonLabel ?? DEFAULT_TOP_BUTTON_LABEL}吗？`,
                promise: () => Promise.resolve(props?.topService?.(row) || row),
                successMessage: `${props.topButtonLabel ?? DEFAULT_TOP_BUTTON_LABEL}成功`,
              });
            },
            (row) => (props.canTop ? props.canTop(row) : true)
          ),
        ]
      : []),
    ...(canUntop.value
      ? [
          mergeRowInternalOperation(
            "untop",
            "link",
            props.untopButtonLabel ?? DEFAULT_UNTOP_BUTTON_LABEL,
            "primary",
            async (row) => {
              await handleWithConfirmWithMessage({
                message: `确认${props.untopButtonLabel ?? DEFAULT_UNTOP_BUTTON_LABEL}吗？`,
                promise: () => Promise.resolve(props?.untopService?.(row) || row),
                successMessage: `${props.untopButtonLabel ?? DEFAULT_UNTOP_BUTTON_LABEL}成功`,
              });
            },
            (row) => (props.canUntop ? props.canUntop(row) : true)
          ),
        ]
      : []),
    ...(canApprove.value
      ? [
          mergeRowInternalOperation(
            "approve",
            "link",
            props.approveButtonLabel ?? DEFAULT_APPROVE_BUTTON_LABEL,
            "primary",
            async (row) => {
              await handleWithConfirmWithMessage({
                message: `确认${props.approveButtonLabel ?? DEFAULT_APPROVE_BUTTON_LABEL}吗？`,
                promise: () => Promise.resolve(props?.approveService?.(row) || row),
                successMessage: `${props.approveButtonLabel ?? DEFAULT_APPROVE_BUTTON_LABEL}成功`,
              });
            },
            (row) => (props.canApprove ? props.canApprove(row) : true)
          ),
        ]
      : []),
    ...(canReject.value
      ? [
          mergeRowInternalOperation(
            "reject",
            "link",
            props.rejectButtonLabel ?? DEFAULT_REJECT_BUTTON_LABEL,
            "warning",
            async (row) => {
              await handleWithConfirmWithMessage({
                message: `确认${props.rejectButtonLabel ?? DEFAULT_REJECT_BUTTON_LABEL}吗？`,
                promise: () => Promise.resolve(props?.rejectService?.(row) || row),
                successMessage: `${props.rejectButtonLabel ?? DEFAULT_REJECT_BUTTON_LABEL}成功`,
              });
            },
            (row) => (props.canReject ? props.canReject(row) : true)
          ),
        ]
      : []),
    ...(canDelete.value
      ? [
          mergeRowInternalOperation(
            "delete",
            "link",
            props.deleteButtonLabel ?? DEFAULT_DELETE_BUTTON_LABEL,
            "danger",
            async (row) => {
              await handleWithConfirmWithMessage({
                message: `确认${props.deleteButtonLabel ?? DEFAULT_DELETE_BUTTON_LABEL}吗？`,
                promise: () => Promise.resolve(props?.deleteService?.(row) || row),
                successMessage: `${props.deleteButtonLabel ?? DEFAULT_DELETE_BUTTON_LABEL}成功`,
              });
            },
            props.canDelete ?? undefined
          ),
        ]
      : []),
    ...(props.rowOperations && props.rowOperations.length > 0
      ? props.rowOperations.filter((s) => !s.overwriteInternalType)
      : []),
  ].sort((a, b) => (a.displayIndex ?? 0) - (b.displayIndex ?? 0))
);
const slots = useSlots();

/** 对外抛出刷新表格的方法，以供自定义操作栏时使用 */
const handleRefresh = () => {
  searchPageRef.value.refresh?.();
};

// 在需要的地方获取（比如方法中或 watch 中）
const getCurrentFormData = () => {
  if (!searchPageRef.value) {
    console.error("SearchPage 组件未初始化");
    return {};
  }
  return searchPageRef.value.getFormData();
};

defineExpose({
  /** 刷新表格 */
  handleRefresh,
  selectedRows,
  getCurrentFormData,
});
</script>

<template>
  <div class="entity-crud-container">
    <EditableForm v-bind="createFormProps">
      <template v-for="(_, name) in slots" :key="name" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </EditableForm>
    <EditableForm v-bind="updateFormProps">
      <template v-for="(_, name) in slots" :key="name" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </EditableForm>
    <DisplayableDialog v-bind="detailItemProps" />
    <SearchPage
      ref="searchPageRef"
      :active-refresh="false"
      v-bind="searchPageProps"
      @table-selection-change="handleSelectionChange"
    >
      <template v-for="(_, name) in slots" :key="name" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
      <template #tableEmpty>暂无数据</template>
      <template #tableOperations="{ row }">
        <template v-for="operation in rowOperations" :key="operation.label">
          <el-button
            v-if="
              operation.type === 'button' &&
              (operation.canDisplay ? operation.canDisplay(row) : true)
            "
            :type="
              operation.colorize
                ? (operation.colorize(row) as
                    | 'primary'
                    | 'success'
                    | 'warning'
                    | 'danger'
                    | 'info'
                    | 'text'
                    | 'default')
                : 'primary'
            "
            @click="operation.actionService(row)"
          >
            {{ operation.label }}
          </el-button>
          <el-button
            v-else-if="
              operation.type === 'link' && (operation.canDisplay ? operation.canDisplay(row) : true)
            "
            link
            :type="
              operation.colorize
                ? (operation.colorize(row) as
                    | 'primary'
                    | 'success'
                    | 'warning'
                    | 'danger'
                    | 'info'
                    | 'text'
                    | 'default')
                : 'primary'
            "
            @click="operation.actionService(row)"
          >
            {{ operation.label }}
          </el-button>
        </template>
      </template>
    </SearchPage>
  </div>
</template>
<style lang="scss" scoped>
.entity-crud-container {
  padding: 20px;
}
</style>
