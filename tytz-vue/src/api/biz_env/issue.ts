import request from "@/utils/request";

const API_PREFIX = "/api/v1/problems";

/**
 * 问题分页查询对象
 */
export interface ProblemsPageQuery extends PageQuery {
  /** 标题 */
  title?: string;

  /** 类别 */
  businessType?: string;

  /** 商会会员名称 */
  memberName?: string;

  /**所属部门 */
  department?: string;

  /**采纳状态 */
  adoptStatus?: string;

  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;

  ids?: any;
}

/**问题分页对象 */
export interface ProblemsPageVO {
  /** 主键 */
  id?: number;

  /** 营商环境标题 */
  title?: string;

  /** 营商环境问题内容 */
  content?: string;

  /** 营商环境类别 */
  businessType?: string;

  /** 提交时间 */
  submitTime?: string;

  /** 商会会员名称 */
  memberName?: string;

  /** 所属部门 */
  department?: string;

  /** 采纳状态 */
  adoptStatus?: string;

  /** 采纳意见 */
  adoptContent?: string;

  /** 采纳人 */
  adoptBy?: string;

  /** 采纳时间 */
  adoptTime?: string;

  /** 批示状态 */
  instructionStatus?: string;

  /** 批示内容 */
  instructionContent?: string;

  /** 批示人 */
  instructionBy?: string;

  /** 批示时间 */
  instructionTime?: string;

  /** 领导批示 */
  leaderInstruction?: string;

  /** 创建时间 */
  createTime?: string;

  /**用户身份 */
  memberRole?: string;
}
export const problemsPageApi = (queryParams: ProblemsPageQuery) => {
  return request<any, PageResult<ProblemsPageVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/** 查看问题审核表单数据*/
export const problemsViewApi = (id: string) => {
  return request<any, ProblemsPageVO>({
    url: `${API_PREFIX}/${id}/form`,
    method: "get",
  });
};

/**导出 */
export const problemsExportApi = (queryParams: any) => {
  return request<any, Blob>({
    url: `${API_PREFIX}/export`,
    method: "POST",
    data: queryParams,
    responseType: "blob",
  });
};

export interface adoptProblemsForm {
  /**主键 */
  id?: string;

  /**采纳意见 */
  adoptContent?: string;

  /**采纳状态 */
  adoptStatus?: string;
}

/** 采纳问题审核*/
export const problemsAdoptApi = (params: adoptProblemsForm) => {
  return request<any, adoptProblemsForm>({
    url: `${API_PREFIX}/adopt`,
    method: "PUT",
    data: params,
  });
};
