package com.youlai.boot.modules.work.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.WorkTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * 年度重点工作分页查询对象
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Schema(description ="年度重点工作查询对象")
@Getter
@Setter
public class KeyWorkQuery extends BasePageQuery {
    
    @Schema(description = "年度")
    private Integer year;
    
    @Schema(description = "工作名称")
    private String workName;

    @Schema(description = "工作类型")
    private WorkTypeEnum workType;
    
    @Schema(description = "负责部门")
    private String department;
    
    @Schema(description = "开始时间")
    private String startTime;
    
    @Schema(description = "结束时间")
    private String endTime;
} 