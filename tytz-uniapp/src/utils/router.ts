import pages from '@/pages.json'

/**
 * 获取当前页面
 * @returns 当前页面
 */
export function getCurrentPage() {
  const pages = getCurrentPages()
  return pages.at(-1)
}

/**
 * 获取当前页面配置
 * @returns 当前页面配置
 */
export function getCurrentPageConfig() {
  const allPages = [...pages.pages]
  const currentPage = getCurrentPage()
  const currentPageConfig = allPages.find((page) => page.path === currentPage?.route)
  return currentPageConfig
}

/**
 * 获取页面配置
 * @param path 页面路径
 * @returns 页面配置
 */
export function getPageConfig(path: string) {
  const allPages = [...pages.pages]
  return allPages.find((page) => page.path === path)
}

/**
 * 判断当前页面是不是tabbar页面
 * @returns 是否是tabbar页面
 */
export function isTabbarPage() {
  const currentPage = getCurrentPage()
  return pages.tabBar.list.some((item) => item.pagePath === currentPage?.route)
}
