<script setup lang="ts">
import { ref } from "vue";
import { defineEntityCrud } from "@/components/EntityCrud/hook";
import EntityCrud from "@/components/EntityCrud/index.vue";
import { EditableItem, EntityCrudProps } from "@/components/EntityCrud/type";
import { getEnumOptions } from "@/components/EntityCrud/util";
import displayableDialog from "@/components/EntityCrud/DisplayableDialog/index.vue";
import type { DisplayableItem } from "@/components/EntityCrud/DisplayableDialog/type";
import {
  annualKeyTasksPageVO,
  annualKeyTasksPageApi,
  annualKeyTasksAddApi,
  annualKeyTasksUpdateApi,
  annualKeyTasksDeleteApi,
  annualKeyTasksViewApi,
} from "@/api/rating/annualKeyTasks";
//import UserAPI from "@/api/system/user";
import dayjs from "dayjs";
import DeptAPI from "@/api/system/dept";
const typeEnum = {
  PROJECT_SERVICE: "助力项目建设服务，参加引进外资活动",
  BUSINESS_ENVIRONMENT: "助推创一流营商环境",
  OTHER_TASKS: "完成区商会交办的其他任务",
};

const entityCrudRef = ref<any>(null);

const dialogVisible = ref(false);
const detailData = ref<annualKeyTasksPageVO | null>(null);
const displayItems: DisplayableItem[] = [
  {
    label: "年度工作",
    prop: "workName",
    type: "text",
  },
  {
    label: "工作类型",
    prop: "workType",
    type: "text",
    formatter: (value: string) => typeEnum[value as keyof typeof typeEnum],
  },
  {
    label: "参加人员",
    prop: "participants",
    type: "text",
  },
  {
    label: "工作时间",
    prop: "participationTime",
    type: "date-range",
  },
  {
    label: "工作内容",
    prop: "workContent",
    type: "rich-text",
  },
  {
    label: "附件",
    prop: "attachments",
    type: "documents",
  },
];
const FormItems = (): EditableItem<annualKeyTasksPageVO> => {
  return {
    id: {
      type: "id",
    },
    year: {
      type: "input",
      label: "年度",
      required: true,
    },
    workName: {
      type: "input",
      label: "年度重点工作名称",
      required: true,
    },
    workType: {
      type: "select",
      label: "工作类型",
      options: getEnumOptions(typeEnum),
      required: true,
    },
    participants: {
      type: "select",
      label: "参加人员",
      required: true,
    },
    participationTime: {
      type: "date-picker",
      label: "参加时间",
      required: true,
    },
    workContent: {
      type: "editor",
      label: "工作内容",
      required: true,
    },
    attachments: {
      type: "multiple-document",
      label: "附件",
      maxFileCount: 10,
      tip: "(注：最多上传10个文件，可上传.pdf、.jpg、.png、.xlsx、.docx、.xls、.doc格式)",
    },
  };
};

const config: EntityCrudProps<annualKeyTasksPageVO> = {
  entityName: "annualKeyTasks",
  displayName: "年度重点工作",
  hasIndexColumn: true,
  filterFormItems: {
    year: {
      type: "input",
      label: "年度",
    },
    workName: {
      type: "input",
      label: "工作名称",
    },
    participationTime: {
      type: "date-range",
      label: "参加时间",
      dateRangeFields: ["startTime", "endTime"],
    },
  },
  rowOperations: [
    {
      label: "详情",
      type: "link",
      displayIndex: -1,
      actionService: async (row) => {
        const result: annualKeyTasksPageVO = await annualKeyTasksViewApi(row.id);
        result.workType = typeEnum[result.workType as keyof typeof typeEnum];
        result.participationTime = [result.startTime, result.endTime];
        result.participants = result.participants
          .map(
            (participant: any) => participant.businessName + "(" + participant.businessMember + ")"
          )
          .join("，");
        detailData.value = result;
        dialogVisible.value = true;
      },
    },
  ],
  createButtonLabel: "新增",
  tableColumns: {
    year: "年度",
    workName: "工作名称",
    workType: {
      label: "工作类型",
      formatter: (value: any) => {
        const key = value as keyof typeof typeEnum;
        return typeEnum[key];
      },
    },
    startTime: {
      label: "开始时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    endTime: {
      label: "结束时间",
      formatter: (value: any) => {
        return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      },
    },
  },

  listFetchService: async (params) => {
    const result: any = await annualKeyTasksPageApi(params);
    result.list.forEach((item: any) => {
      item.attachments = item.attachments ? JSON.parse(item.attachments) : [];
      item.participationTime = [item.startTime, item.endTime];
      item.participants = item.participants
        ? JSON.parse(item.participants).map((participant: any) => participant.id.toString()) // 确保是字符串格式
        : [];
    });
    return Promise.resolve(result);
  },
  createFormItems: FormItems(),
  createService: (newRecord: annualKeyTasksPageVO) => {
    if (newRecord.participationTime && newRecord.participationTime.length >= 2) {
      const [startDate, endDate] = newRecord.participationTime;
      newRecord.startTime = `${startDate} 00:00:00`;
      newRecord.endTime = `${endDate} 23:59:59`;
    }
    if (Array.isArray(newRecord.participants)) {
      newRecord.participants = newRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete newRecord.participationTime;
    return annualKeyTasksAddApi(newRecord);
  },
  useCreateFormItemsAsUpdate: true,
  updateService: (updateRecord: annualKeyTasksPageVO) => {
    if (updateRecord.participationTime && updateRecord.participationTime.length >= 2) {
      const [startDate, endDate] = updateRecord.participationTime;
      updateRecord.startTime = startDate.includes("T")
        ? startDate.replace("T", " ")
        : `${startDate} 00:00:00`;
      updateRecord.endTime = endDate.includes("T")
        ? endDate.replace("T", " ")
        : `${endDate} 23:59:59`;
    }
    if (Array.isArray(updateRecord.participants)) {
      updateRecord.participants = updateRecord.participants.map((participant: any) => ({
        id: participant,
        businessName: participant,
        businessMember: participant,
      }));
    }
    delete updateRecord.participationTime;
    return annualKeyTasksUpdateApi(updateRecord.id, updateRecord);
  },
  deleteService: (row: annualKeyTasksPageVO) => {
    return annualKeyTasksDeleteApi(row.id);
  },
};
const entityCrudProps = defineEntityCrud(config);

const memberOptions = ref<{ label: string; value: string }[]>([]);

const getMemberOptions = async () => {
  await DeptAPI.getUsersByDeptId("5").then((res: any) => {
    if (Array.isArray(res)) {
      memberOptions.value = res.map((item: any) => {
        return {
          label: `${item.nickname}（${item.company ? item.company : "-"}）-${item.deptName}`,
          value: item.id,
        };
      });
    }
  });
};

const selectParticipants = ref<any>("");
const handleChangeSelect = (value: any) => {
  selectParticipants.value = value;
};
getMemberOptions();
</script>

<template>
  <div class="entity-crud-wrapper">
    <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
      <template #participants="{ formData, props, change }">
        <el-select
          v-model="formData.participants"
          placeholder="请选择商会成员"
          multiple
          @change="handleChangeSelect"
        >
          <el-option
            v-for="option in memberOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
    </EntityCrud>
    <displayableDialog
      v-model:visible="dialogVisible"
      :display-data="detailData"
      :display-items="displayItems"
      title="详情"
      width="800px"
    />
  </div>
</template>

<style lang="scss" scoped></style>
