import request from '@/utils/request'

/**
 * DataProblemVO
 */
export interface DataNewsVO {
  list?: NewsVO[]
  total?: number
}

export interface NewsVO {
  id?: string
  /** 新闻标题 */
  title?: string

  /** 新闻内容 */
  content?: string

  /** 发布状态 */
  publishStatus?: string

  /** 附件 */
  attachments?: string

  /** 创建时间 */
  createTime?: string

  /** 更新时间 */
  updateTime?: string

  /** 发布者 */
  createBy?: string
}

/**
 * 获取新闻资讯列表
 * @param params
 * @returns
 */
export const getNewsPage = (params: {
  pageNum: number
  pageSize: number
  publishStatus: string
}) => {
  return request<DataNewsVO>('/api/v1/news/page', {
    method: 'GET',
    data: params,
  })
}

/**
 * 获取新闻资讯详情
 * @param id
 * @returns
 */
export const getNewsDetail = (id: string) => {
  return request<NewsVO>(`/api/v1/news/${id}/form`, {
    method: 'GET',
  })
}
