<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.problem.mapper.ProblemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youlai.boot.modules.problem.model.entity.Problem">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="business_type" property="businessType"/>
        <result column="submit_time" property="submitTime"/>
        <result column="member_name" property="memberName"/>
        <result column="department" property="department"/>
        <result column="adopt_status" property="adoptStatus"/>
        <result column="adopt_content" property="adoptContent"/>
        <result column="adopt_by" property="adoptBy"/>
        <result column="adopt_time" property="adoptTime"/>
        <result column="instruction_status" property="instructionStatus"/>
        <result column="instruction_content" property="instructionContent"/>
        <result column="instruction_by" property="instructionBy"/>
        <result column="instruction_time" property="instructionTime"/>
        <result column="leader_instruction" property="leaderInstruction"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, content, business_type, submit_time, member_name, department, 
        adopt_status, adopt_content, adopt_by, adopt_time,
        instruction_status, instruction_content, instruction_by, instruction_time,
        leader_instruction, create_by, create_time, update_by, update_time, is_deleted
    </sql>

    <!-- VO查询映射结果 -->
    <resultMap id="VOResultMap" type="com.youlai.boot.modules.problem.model.vo.ProblemVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="business_type" property="businessType"/>
        <result column="submit_time" property="submitTime"/>
        <result column="member_name" property="memberName"/>
        <result column="department" property="department"/>
        <result column="adopt_status" property="adoptStatus"/>
        <result column="adopt_content" property="adoptContent"/>
        <result column="adopt_by" property="adoptBy"/>
        <result column="adopt_time" property="adoptTime"/>
        <result column="instruction_status" property="instructionStatus"/>
        <result column="instruction_content" property="instructionContent"/>
        <result column="instruction_by" property="instructionBy"/>
        <result column="instruction_time" property="instructionTime"/>
        <result column="leader_instruction" property="leaderInstruction"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 获取分页列表 -->
    <select id="getProblemPage" resultMap="VOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tsz_problem
        <where>
            is_deleted = 0
            <if test="queryParams.title != null and queryParams.title != ''">
                AND title LIKE CONCAT('%', #{queryParams.title}, '%')
            </if>
            <if test="queryParams.businessType != null and queryParams.businessType != ''">
                AND business_type LIKE CONCAT('%', #{queryParams.businessType}, '%')
            </if>
            <if test="queryParams.memberName != null and queryParams.memberName != ''">
                AND member_name LIKE CONCAT('%', #{queryParams.memberName}, '%')
            </if>
            <if test="queryParams.department != null and queryParams.department != ''">
                AND department LIKE CONCAT('%', #{queryParams.department}, '%')
            </if>
            <if test="queryParams.adoptStatus != null">
                AND adopt_status = #{queryParams.adoptStatus}
            </if>
            <if test="queryParams.instructionStatus != null">
                AND instruction_status = #{queryParams.instructionStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper> 