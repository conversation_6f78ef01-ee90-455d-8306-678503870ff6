package com.youlai.boot.modules.problem.model.dto;

import com.youlai.boot.common.enums.AdoptStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ProblemAdoptDTO {
    @Schema(description = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    @Schema(description = "采纳状态")
    @NotNull(message = "采纳状态不能为空")
    private AdoptStatusEnum adoptStatus;

    @Schema(description = "采纳意见")
    private String adoptContent;
}
