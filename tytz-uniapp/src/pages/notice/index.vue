<route lang="json5">
{
  style: {
    navigationBarTitleText: '通知公告',
  },
}
</route>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer/index.vue'
import ArticleCard from '@/components/ArticleCard/index.vue'
import { getNoticePage } from '@/api/notice/index'
defineOptions({
  name: 'NoticeIndex',
})
const loading = ref(false)
const refreshing = ref(false) // 新增：下拉刷新状态
const page = reactive({
  pageNum: 1,
  pageSize: 10,
  publishStatus: 'PUSH',
})
const data = reactive<Awaited<ReturnType<typeof getNoticePage>>>({
  list: [],
  total: 0,
})

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (data.list.length >= data.total) return 'finished'
  return undefined
})

function request(isRefresh = false) {
  loading.value = true
  if (isRefresh) {
    page.pageNum = 1
    data.list = []
    data.total = 0
    uni.showLoading({
      title: '加载中...',
    })
  }
  getNoticePage({
    ...page,
  })
    .then((res) => {
      data.list.push(...res.list)
      data.total = Number(res.total)
      page.pageNum++
    })
    .finally(() => {
      uni.hideLoading()
      loading.value = false
      refreshing.value = false // 重置下拉刷新状态
    })
}

// 新增：下拉刷新处理函数
function handleRefresh() {
  refreshing.value = true
  request(true) // 刷新数据
}

const calculateDaysAgo = (date: string) => {
  const today = new Date()
  const targetDate = new Date(date)
  const timeDiff = Math.abs(today.getTime() - targetDate.getTime())
  const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24))
  const diffHours = Math.ceil(timeDiff / (1000 * 3600))
  const diffMinutes = Math.ceil(timeDiff / (1000 * 60))
  const diffSeconds = Math.ceil(timeDiff / 1000)
  if (diffDays > 0) return `${diffDays}天前`
  if (diffHours > 0) return `${diffHours}小时前`
  if (diffMinutes > 0) return `${diffMinutes}分钟前`
  if (diffSeconds > 0) return `${diffSeconds}秒前`
  return '刚刚'
}

const handleDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/notice/detail?id=${id}`,
  })
}
function handleScrollToLower() {
  if (data.list.length >= data.total) return
  if (loading.value) return
  request()
}
request(true)
</script>

<template>
  <PageContainer>
    <view class="container">
      <!-- 横幅 -->
      <image
        class="w-full mt-20rpx rounded-10rpx"
        mode="widthFix"
        src="@/static/images/news/notice_hengfu.png"
      ></image>
      <scroll-view
        class="mt-10px flex flex-1 overflow-hidden"
        scroll-y
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="handleRefresh"
        @scrolltolower="handleScrollToLower"
      >
        <view
          class="card-list"
          v-for="(item, index) in data.list"
          :key="index"
          @click="handleDetail(item.id)"
        >
          <ArticleCard
            :title="item.title"
            :imageUrl="true"
            :publisher="item.createBy"
            :commentCount="0"
            :createTime="calculateDaysAgo(item.createTime)"
          ></ArticleCard>
        </view>
        <wd-loadmore :state="loadMoreState" />
      </scroll-view>
    </view>
  </PageContainer>
</template>

<style scoped>
.container {
  padding: 0 20rpx;
}
.card-list {
  margin: 40rpx 0;
  border-bottom: 1rpx solid #eee;
}
</style>
