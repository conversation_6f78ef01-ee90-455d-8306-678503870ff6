<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.notice.mapper.TyTzNoticeMapper">

    <!-- 获取通知公告分页列表 -->
    <select id="getNoticePage" resultType="com.youlai.boot.modules.notice.model.vo.TyTzNoticeVO">
        SELECT
            n.*,
            u.nickname as createBy
        FROM
            tsz_notice n
            LEFT JOIN sys_user u ON n.create_by = u.id
        WHERE
            n.is_deleted = 0
        <if test="queryParams.keywords != null and queryParams.keywords != ''">
            AND n.title LIKE CONCAT('%', #{queryParams.keywords}, '%')
        </if>
        <if test="queryParams.publishStatus != null">
            AND n.publish_status = #{queryParams.publishStatus}
        </if>
        <if test="queryParams.startTime != null">
            AND n.create_time &gt;= #{queryParams.startTime}
        </if>
        <if test="queryParams.endTime != null">
            AND n.create_time &lt;= #{queryParams.endTime}
        </if>
        ORDER BY
            n.create_time DESC

    </select>

</mapper>
