package com.youlai.boot.modules.businessmaterials.controller;

import com.youlai.boot.modules.businessmaterials.service.BusinessMaterialsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.youlai.boot.modules.businessmaterials.model.form.BusinessMaterialsForm;
import com.youlai.boot.modules.businessmaterials.model.query.BusinessMaterialsQuery;
import com.youlai.boot.modules.businessmaterials.model.vo.BusinessMaterialsVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * 营商材料前端控制层
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Tag(name = "营商材料接口")
@RestController
@RequestMapping("/api/v1/businessMaterials")
@RequiredArgsConstructor
public class BusinessMaterialsController  {

    private final BusinessMaterialsService businessMaterialsService;

    @Operation(summary = "营商材料分页列表")
    @GetMapping("/page")
//    @PreAuthorize("@ss.hasPerm('modules.businessmaterials:businessMaterials:query')")
    public PageResult<BusinessMaterialsVO> getBusinessMaterialsPage(BusinessMaterialsQuery queryParams ) {
        IPage<BusinessMaterialsVO> result = businessMaterialsService.getBusinessMaterialsPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增营商材料")
    @PostMapping
//    @PreAuthorize("@ss.hasPerm('modules.businessmaterials:businessMaterials:add')")
    public Result<Void> saveBusinessMaterials(@RequestBody @Valid BusinessMaterialsForm formData ) {
        boolean result = businessMaterialsService.saveBusinessMaterials(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取营商材料表单数据")
    @GetMapping("/{id}/form")
//    @PreAuthorize("@ss.hasPerm('modules.businessmaterials:businessMaterials:edit')")
    public Result<BusinessMaterialsForm> getBusinessMaterialsForm(
        @Parameter(description = "营商材料ID") @PathVariable Long id
    ) {
        BusinessMaterialsForm formData = businessMaterialsService.getBusinessMaterialsFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改营商材料")
    @PutMapping(value = "/{id}")
//    @PreAuthorize("@ss.hasPerm('modules.businessmaterials:businessMaterials:edit')")
    public Result<Void> updateBusinessMaterials(
            @Parameter(description = "营商材料ID") @PathVariable Long id,
            @RequestBody @Validated BusinessMaterialsForm formData
    ) {
        boolean result = businessMaterialsService.updateBusinessMaterials(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除营商材料")
    @DeleteMapping("/{ids}")
//    @PreAuthorize("@ss.hasPerm('modules.businessmaterials:businessMaterials:delete')")
    public Result<Void> deleteBusinessMaterialss(
        @Parameter(description = "营商材料ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = businessMaterialsService.deleteBusinessMaterialss(ids);
        return Result.judge(result);
    }
}
