package com.youlai.boot.modules.problem.model.vo;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;

import lombok.Data;

@Data
public class ProblemExcelVO {
    @ExcelProperty("序号")
    private Long index;

    @ExcelProperty("商会成员名称")
    private String memberName;

    @ExcelProperty("所属单位")
    private String department;

    @ExcelProperty("营商环境类别")
    private String businessType;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("营商环境内容")
    private String content;

    @ExcelProperty("提交时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;
} 