package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 数据权限枚举
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Getter
public enum AppoinitMentEnum implements IBaseEnum<String> {


    WAIT("WAIT", "待约见"),
    WAIT_START("WAIT_START", "待开始"),
    MEETING("MEETING", "约见中"),
    END("END", "已结束"),
    REJECT("REJECT", "不予约见");

    private final String value;

    private final String label;

    AppoinitMentEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
