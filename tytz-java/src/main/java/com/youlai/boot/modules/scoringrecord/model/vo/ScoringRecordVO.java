package com.youlai.boot.modules.scoringrecord.model.vo;

import lombok.*;


import io.swagger.v3.oas.annotations.media.Schema;


@Data
@Schema(name = "评分记录VO")
public class ScoringRecordVO {

    /**
     * 主键
     */
    @Schema(name = "主键")
    private Long id;

    /**
     * 年度
     */
    @Schema(name = "年度")
    private String year;
    /**
     * 商会会员名称
     */
    @Schema(name = "商会会员名称")
    private String memberName;

    /**
     * 所属部门
     */
    @Schema(name = "所属部门")
    private String department;

    /**
     * 参加活动（次）
     */
    @Schema(name = "参加活动（次）")
    private Long activeCount;

    /**
     * 参加会议（次）
     */
    @Schema(name = "参加会议（次）")
    private Long meetingCount;

    /**
     * 年度重点工作（次）
     */
    @Schema(name = "年度重点工作（次）")
    private Long keyWorkCount;

    /**
     * 营商环境问题报送（次）
     */
    @Schema(name = "营商环境问题报送（次）")
    private Long environmentCount;

    /**
     * 总得分
     */
    @Schema(name = "总得分")
    private Long totalScore;

    @Schema(name = "商户会员id")
    private Long memberId;

} 