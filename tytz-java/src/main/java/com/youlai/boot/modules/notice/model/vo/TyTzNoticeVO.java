package com.youlai.boot.modules.notice.model.vo;

import java.io.Serial;
import java.io.Serializable;

import com.youlai.boot.common.enums.PublishStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 通知公告视图对象
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Getter
@Setter
@Schema( description = "通知公告视图对象")
public class TyTzNoticeVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;
    @Schema(description = "通知标题")
    private String title;
    @Schema(description = "通知内容")
    private String content;
    @Schema(description = "发布状态")
    private PublishStatusEnum publishStatus;
    @Schema(description = "附件JSON数据，包含文件名、路径等信息")
    private String attachments;
    @Schema(description = "创建人")
    private String createBy;
    @Schema(description = "创建时间")
    private String createTime;
}
