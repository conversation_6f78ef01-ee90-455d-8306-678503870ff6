package com.youlai.boot.modules.activity.converter;

import org.mapstruct.Mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.activity.model.entity.Activity;
import com.youlai.boot.modules.activity.model.form.ActivityForm;
import org.mapstruct.Mapping;

/**
 * 活动管理对象转换器
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Mapper(componentModel = "spring")
public interface ActivityConverter {
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    ActivityForm toForm(Activity entity);

    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    Activity toEntity(ActivityForm formData);
} 