package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 数据权限枚举
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Getter
public enum InstructionStatusEnum implements IBaseEnum<String> {


    WAIT("WAIT", "待批示"),
    PASS("PASS", "已批示"),
    REJECT("REJECT", "批示未通过");

    private final String value;

    private final String label;

    InstructionStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
