package com.youlai.boot.modules.scoringrecord.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 评分记录导出VO
 */
@Data
public class ScoringRecordExportVO {

    /**
     * 序号
     */
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private Long index;

    /**
     * 年度
     */
    @ExcelProperty(value = "年度", index = 1)
    @ColumnWidth(15)
    private String year;

    /**
     * 商会会员名称
     */
    @ExcelProperty(value = "会员名称", index = 2)
    @ColumnWidth(20)
    private String memberName;

    /**
     * 所属部门
     */
    @ExcelProperty(value = "所属单位", index = 3)
    @ColumnWidth(25)
    private String department;

    /**
     * 参加活动（次）
     */
    @ExcelProperty(value = "参加活动（次）", index = 4)
    @ColumnWidth(15)
    private Long activeCount;

    /**
     * 参加会议（次）
     */
    @ExcelProperty(value = "参加会议（次）", index = 5)
    @ColumnWidth(15)
    private Long meetingCount;

    /**
     * 年度重点工作（次）
     */
    @ExcelProperty(value = "年度重点工作（次）", index = 6)
    @ColumnWidth(20)
    private Long keyWorkCount;

    /**
     * 营商环境问题报送（次）
     */
    @ExcelProperty(value = "营商环境问题报送（次）", index = 7)
    @ColumnWidth(25)
    private Long environmentCount;

    /**
     * 总得分
     */
    @ExcelProperty(value = "总得分", index = 8)
    @ColumnWidth(15)
    private Long totalScore;
}
