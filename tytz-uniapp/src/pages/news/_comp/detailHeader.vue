<script lang="ts" setup>
export interface DetailHeaderProps {
  /**
   * 标题
   */
  title?: string
  /**
   * 是否显示专题图标
   */
  imageUrl?: boolean
  /**
   * 发布者
   */
  publisher?: string
  /**
   * 评论次数
   */
  commentCount?: number
  /**
   * 发布时间  *
   *
   */
  createTime?: string
}

const props = defineProps<DetailHeaderProps>()
</script>

<template>
  <view class="container">
    <view class="header">{{ title }}</view>
    <view class="flex mt-30rpx mb-30rpx">
      <view class="text-sm text-gray-500 mr-10rpx">{{ publisher }}</view>
      <!-- <view>{{ commentCount }} 评论</view> -->
      <view class="text-sm text-gray-500">{{ createTime }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.header {
  font-size: 35rpx;
}
</style>
