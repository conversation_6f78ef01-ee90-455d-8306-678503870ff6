import { computed, ref, watch } from "vue";

import { DIALOG_TITLE, DIALOG_TYPE } from "./config";
import type { DialogPageProps, FormItemsUnionTypeList } from "./type";

export interface UseEditableFormOptions<T> {
  /** 表单配置项 */
  formItems: FormItemsUnionTypeList;
  /** 初始数据服务 */
  initialDataService?: (originalData: T) => Promise<any>;
  /** 新增请求 */
  addRequest?: (data: T) => Promise<any>;
  /** 编辑请求 */
  editRequest?: (data: T) => Promise<any>;
  /** 成功回调 */
  onSuccess?: (data?: any) => void;
  /** 表单宽度 */
  width?: string | number;
  /** 自定义标题 */
  customTitle?: string;
  /** 是否点击遮罩层关闭 */
  closeOnClickModal?: boolean;
  /** 是否点击 ESC 关闭 */
  closeOnPressEscape?: boolean;
}

/**
 * EditableForm 组件的 Hook
 */
export function useEditableForm<T extends Record<string, any>>(options: UseEditableFormOptions<T>) {
  const visible = ref(false);
  const type = ref<(typeof DIALOG_TYPE)[keyof typeof DIALOG_TYPE]>(DIALOG_TYPE.ADD);
  const initialData = ref<Partial<T>>();
  const formData = ref<T>();

  const title = computed(() => {
    if (options.customTitle) return options.customTitle;
    return type.value ? DIALOG_TITLE[type.value] : "";
  });

  const formProps = computed(
    (): DialogPageProps<T> => ({
      visible: visible.value,
      "onUpdate:visible": (val: boolean) => {
        visible.value = val;
      },
      type: type.value,
      title: title.value,
      width: options.width || "500px",
      formItems: options.formItems,
      initialData: initialData.value,
      initialDataService: options.initialDataService,
      service: type.value === DIALOG_TYPE.ADD ? options.addRequest : options.editRequest,
      onBeforeSubmit: (data: T) => {
        formData.value = data;
      },
      onSuccess: (data: any) => {
        options.onSuccess?.(data);
        setTimeout(() => {
          type.value = DIALOG_TYPE.ADD;
          initialData.value = undefined;
        }, 300);
      },
      onCancel: () => {
        formData.value = undefined;
        setTimeout(() => {
          type.value = DIALOG_TYPE.ADD;
          initialData.value = undefined;
        }, 300);
      },
    })
  );

  function newEditable() {
    type.value = DIALOG_TYPE.ADD;
    initialData.value = undefined;
    visible.value = true;

    return new Promise<T>((resolve, reject) => {
      const unwatch = watch(visible, (newVisible) => {
        if (!newVisible) {
          unwatch();
          if (formData.value !== undefined) {
            resolve(formData.value);
          } else {
            reject(new Error("Form data is undefined"));
          }
        }
      });
    });
  }

  function editEditable(data: Partial<T>) {
    type.value = DIALOG_TYPE.EDIT;
    initialData.value = data;
    visible.value = true;

    return new Promise<T>((resolve, reject) => {
      const unwatch = watch(visible, (newVisible) => {
        if (!newVisible) {
          unwatch();
          if (formData.value !== undefined) {
            resolve(formData.value);
          } else {
            reject(new Error("Form data is undefined"));
          }
        }
      });
    });
  }

  return {
    visible,
    formProps,
    newEditable,
    editEditable,
  };
}
