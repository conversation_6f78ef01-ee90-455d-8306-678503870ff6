import { CustomRequestOptions } from '@/interceptors/request'
import { ResultEnum } from '@/enums/ResultEnum'
import { merge } from 'lodash-es'
import { TOKEN_TYPE, ACCESS_TOKEN, REFRESH_TOKEN } from '@/const/storage'
import { useNewUserStoreHook } from '@/store/modules/user'

const userStore = useNewUserStoreHook()

function showTips(msg: string, url: string) {
  const notMsg = ['api/v1/auth/refresh-token']
  // const { token } = userStore.userInfo as unknown as IUserInfo
  if (notMsg.some((item) => url.includes(item))) {
    return
  }
  /*   if (!token) {
    return useNavigate('/pages/login/index', {}, 'navigateTo')
  } */
  uni.showToast({
    icon: 'none',
    title: msg || '网络错误，换个网络试试',
  })
}

/**
 * 请求方法: 主要是对 uni.request 的封装，去适配 openapi-ts-request 的 request 方法
 * @param options 请求参数
 * @returns 返回 Promise 对象
 */
const http = <T>(options: CustomRequestOptions) => {
  // // 1. 返回 Promise 对象
  return new Promise<T>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        const data = res.data as { code: string; data: T; msg: string }
        if (res.statusCode !== 200) {
          showTips(data.msg, options.url)
          /*  return reject(res) */
        }
        if (data.code === ResultEnum.SUCCESS) {
          resolve(data.data)
        } else if (data.code === ResultEnum.ERROR) {
          showTips(data.msg, options.url)
          return reject(res)
        } else if (
          data.code === ResultEnum.ACCESS_TOKEN_INVALID ||
          data.code === ResultEnum.REFRESH_TOKEN_INVALID
        ) {
          userStore.clearUserInfo()
          uni.removeStorageSync(TOKEN_TYPE)
          uni.removeStorageSync(ACCESS_TOKEN)
          uni.removeStorageSync(REFRESH_TOKEN)
          /*  uni.redirectTo({
            url: '/pages/person/index',
          }) */
          return useNavigate('/pages/login/index', {}, 'navigateTo')
        }
      },
      // 响应失败
      fail(err) {
        showTips(err.errMsg, options.url)
        reject(err)
      },
    })
  })
}

/*
 * openapi-ts-request 工具的 request 跨客户端适配方法
 */
export default function request<T = unknown>(
  url: string,
  options: Omit<CustomRequestOptions, 'url'> & {
    params?: Record<string, unknown>
    header?: Record<string, unknown>
  },
) {
  const accessToken = uni.getStorageSync(ACCESS_TOKEN)
  const Authorization = accessToken ? `${uni.getStorageSync(TOKEN_TYPE)} ${accessToken}` : ''

  const requestOptions = merge({
    url: `${import.meta.env.VITE_APP_PROXY_PREFIX}${url}`,
    header: {
      Authorization,
    },
    ...options,
  })

  return http<T>(requestOptions)
}
