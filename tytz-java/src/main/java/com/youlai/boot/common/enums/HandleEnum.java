package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 数据权限枚举
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Getter
public enum HandleEnum implements IBaseEnum<String> {

    WAIT("WAIT", "待处理"),
    HANDLING("HANDLING", "处理中"),
    PASS("PASS", "处理通过"),
    REJECT("REJECT", "处理驳回");

    private final String value;

    private final String label;

    HandleEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
