package com.youlai.boot.modules.scoringrecord.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评分记录分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ScoringRecordPageQuery extends BasePageQuery {

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 部门
     */
    private String department;

    /**
     * 年度
     */
    private String year;

    /**
     * 评分类型
     */
    private ScoreTypeEnum scoringType;

    /**
     * 评分记录ID列表，多个以英文逗号(,)分割
     */
    private String ids;

    /**
     * 会员部门编号
     */
    private String memberDeptCode;

}