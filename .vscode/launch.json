{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "Tytz Vue",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "type": "java",
      "name": "Spring Boot-YouLaiBootApplication<youlai-boot>",
      "request": "launch",
      "cwd": "${workspaceFolder}",
      "mainClass": "com.youlai.boot.YouLaiBootApplication",
      "projectName": "youlai-boot",
      "args": "",
      "envFile": "${workspaceFolder}/.env"
    }
  ]
}
