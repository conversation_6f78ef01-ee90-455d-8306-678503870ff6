import request from '@/utils/request'

/**
 * DataOpinionVO
 */
export interface DataAppointmentVO {
  list?: AppointmentVO[]
  total?: number
}

/**
 * 约见申请对象
 *
 * OpinionVO
 */
export interface AppointmentVO {
  /**
   * 约见人名称
   */
  appointmentName?: string
  /**
   * 所属单位
   */
  appointmentUnit?: string
  /**
   * 联系方式
   */
  appointmentContact?: string
  /**
   * 被约见部委
   */
  appointmentDepartment?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 约见原因
   */
  appointmentReason?: string
  /**
   * 约见开始时间
   */
  appointmentStartTime?: string
  /**
   * 约见结束时间
   */
  appointmentEndTime?: string
  /**
   * 约见状态
   */
  appointmentStatus?: string
}

/**
 * 约见申请
 * @param data
 */
export const createAppointment = (data: Record<string, any>) => {
  return request('/api/v1/appointments', {
    method: 'POST',
    data,
  })
}

/** 约见申请分页列表 */
export const getAppointmentPage = (params: {
  pageNum: number
  pageSize: number
  keyword?: string
}) => {
  return request<DataAppointmentVO>('/api/v1/appointments/my/page', {
    method: 'GET',
    data: params,
  })
}

/**
 * 约见申请详情
 * @param id
 * @returns
 */
export const getAppointmentForm = (id: string) => {
  return request<AppointmentVO>(`/api/v1/appointments/${id}/form`, {
    method: 'GET',
  })
}
