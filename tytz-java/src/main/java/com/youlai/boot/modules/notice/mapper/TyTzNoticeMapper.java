package com.youlai.boot.modules.notice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youlai.boot.modules.notice.model.entity.TyTzNotice;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.notice.model.query.TyTzNoticeQuery;
import com.youlai.boot.modules.notice.model.vo.TyTzNoticeVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通知公告Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-10 14:17
 */
@Mapper
public interface TyTzNoticeMapper extends BaseMapper<TyTzNotice> {

    /**
     * 获取通知公告分页数据
     *
     * @param page 分页对象
     * @param queryParams 查询参数
     * @return
     */
    Page<TyTzNoticeVO> getNoticePage(Page<TyTzNoticeVO> page, TyTzNoticeQuery queryParams);

}
