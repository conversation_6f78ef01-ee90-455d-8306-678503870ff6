package com.youlai.boot.modules.scoringrecord.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.ScoreTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评分记录详情查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "评分记录详情查询对象")
public class ScoringRecordDetailQuery extends BasePageQuery {

    /**
     * 评分记录ID列表，多个以英文逗号(,)分割
     */
    @Schema(description = "评分记录ID列表，多个以英文逗号(,)分割")
    private String ids;

    /**
     * 年度
     */
    @Schema(description = "年度")
    private String year;

    /**
     * 会员ID
     */
    @Schema(description = "会员ID")
    private Long memberId;

    /**
     * 评分类型
     */
    @Schema(description = "评分类型")
    private ScoreTypeEnum scoringType;
}
