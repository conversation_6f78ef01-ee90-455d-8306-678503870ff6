package com.youlai.boot.modules.businessmaterials.model.form;

import java.io.Serial;
import java.io.Serializable;

import com.youlai.boot.modules.notice.model.form.TyTzNoticeForm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.validation.constraints.*;

/**
 * 营商材料表单对象
 *
 * <AUTHOR>
 * @since 2025-03-10 16:58
 */
@Getter
@Setter
@Schema(description = "营商材料表单对象")
public class BusinessMaterialsForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "材料名称")
    @NotBlank(message = "材料名称不能为空")
    @Size(max=100, message="材料名称长度不能超过100个字符")
    private String title;

    @Schema(description = "材料内容")
    @NotBlank(message = "材料内容不能为空")
    @Size(max=65535, message="材料内容长度不能超过65535个字符")
    private String content;

    @Schema(description = "提交人ID")
    @NotNull(message = "提交人ID不能为空")
    private Long submitBy;

    @Schema(description = "附件JSON数据，包含文件名、路径等信息")
    @Size(max=65535, message="附件JSON数据，包含文件名、路径等信息长度不能超过65535个字符")
    private List<FileDTO> attachments;

    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    private LocalDateTime activeStartTime;

    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    private LocalDateTime activeEndTime;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileDTO {

        @Schema(description = "文件url")
        String url;

        @Schema(description = "文件类型")
        String type;

    }


}
